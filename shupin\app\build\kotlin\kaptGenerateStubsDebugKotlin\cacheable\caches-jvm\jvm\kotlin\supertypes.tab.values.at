/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer+ *com.libretv.android.data.model.SearchState+ *com.libretv.android.data.model.SearchState+ *com.libretv.android.data.model.SearchState+ *com.libretv.android.data.model.SearchState+ *com.libretv.android.data.model.PlayerState+ *com.libretv.android.data.model.PlayerState+ *com.libretv.android.data.model.PlayerState+ *com.libretv.android.data.model.PlayerState+ *com.libretv.android.data.model.PlayerState android.app.Application androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel okhttp3.Interceptor androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity