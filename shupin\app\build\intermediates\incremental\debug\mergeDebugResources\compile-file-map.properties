#Thu Jun 05 19:49:54 CST 2025
com.libretv.android.app-main-70\:/drawable/ic_launcher_background.xml=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.libretv.android.app-main-70\:/drawable/ic_launcher_foreground.xml=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.libretv.android.app-main-70\:/drawable/ic_no_image.xml=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_no_image.xml.flat
com.libretv.android.app-main-70\:/drawable/ic_placeholder.xml=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_placeholder.xml.flat
com.libretv.android.app-main-70\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.libretv.android.app-main-70\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.libretv.android.app-main-70\:/mipmap-hdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.libretv.android.app-main-70\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.libretv.android.app-main-70\:/mipmap-mdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.libretv.android.app-main-70\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.libretv.android.app-main-70\:/mipmap-xhdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.libretv.android.app-main-70\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.libretv.android.app-main-70\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.libretv.android.app-main-70\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.libretv.android.app-main-70\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.libretv.android.app-main-70\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.libretv.android.app-main-70\:/xml/backup_rules.xml=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.libretv.android.app-main-70\:/xml/data_extraction_rules.xml=D\:\\csxm\\LibreTV\\shupin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
