<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\csxm\LibreTV\shupin\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\csxm\LibreTV\shupin\app\src\main\res"><file name="ic_launcher_background" path="D:\csxm\LibreTV\shupin\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\csxm\LibreTV\shupin\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_no_image" path="D:\csxm\LibreTV\shupin\app\src\main\res\drawable\ic_no_image.xml" qualifiers="" type="drawable"/><file name="ic_placeholder" path="D:\csxm\LibreTV\shupin\app\src\main\res\drawable\ic_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\csxm\LibreTV\shupin\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\csxm\LibreTV\shupin\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\csxm\LibreTV\shupin\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">LibreTV</string><string name="search_hint">搜索你喜欢的视频...</string><string name="search_history">搜索历史</string><string name="search_no_results">未找到相关视频</string><string name="search_try_other_keywords">尝试使用其他关键词</string><string name="search_loading">正在搜索...</string><string name="search_error">搜索失败</string><string name="search_retry">重试</string><string name="search_start_hint">输入关键词开始搜索</string><string name="loading">加载中...</string><string name="error">错误</string><string name="retry">重试</string><string name="cancel">取消</string><string name="confirm">确认</string><string name="clear">清除</string><string name="video_source">来源</string><string name="video_type">类型</string><string name="video_year">年份</string><string name="video_area">地区</string><string name="network_error">网络连接失败</string><string name="server_error">服务器错误</string><string name="timeout_error">请求超时</string></file><file path="D:\csxm\LibreTV\shupin\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Shupin" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="D:\csxm\LibreTV\shupin\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\csxm\LibreTV\shupin\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\csxm\LibreTV\shupin\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\csxm\LibreTV\shupin\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\csxm\LibreTV\shupin\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\csxm\LibreTV\shupin\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>