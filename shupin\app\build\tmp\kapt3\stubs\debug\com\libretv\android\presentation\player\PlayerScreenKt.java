package com.libretv.android.presentation.player;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0006\u001a2\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001ap\u0010\n\u001a\u00020\u00012\b\u0010\u000b\u001a\u0004\u0018\u00010\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00132\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0016H\u0003\u001a&\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\u00162\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u00a8\u0006\u001c"}, d2 = {"PlayerScreen", "", "videoPlayInfo", "Lcom/libretv/android/data/model/VideoPlayInfo;", "onBackPressed", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "viewModel", "Lcom/libretv/android/presentation/player/PlayerViewModel;", "PlayerControls", "playInfo", "playerState", "Lcom/libretv/android/data/model/PlayerState;", "currentPosition", "", "onPreviousEpisode", "onNextEpisode", "onEpisodeSelected", "Lkotlin/Function1;", "", "hasPreviousEpisode", "", "hasNextEpisode", "EpisodeButton", "episodeNumber", "isSelected", "onClick", "app_debug"})
public final class PlayerScreenKt {
    
    /**
     * 播放器页面
     */
    @androidx.compose.runtime.Composable()
    public static final void PlayerScreen(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.model.VideoPlayInfo videoPlayInfo, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackPressed, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.libretv.android.presentation.player.PlayerViewModel viewModel) {
    }
    
    /**
     * 播放器控制界面
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void PlayerControls(com.libretv.android.data.model.VideoPlayInfo playInfo, com.libretv.android.data.model.PlayerState playerState, long currentPosition, kotlin.jvm.functions.Function0<kotlin.Unit> onBackPressed, kotlin.jvm.functions.Function0<kotlin.Unit> onPreviousEpisode, kotlin.jvm.functions.Function0<kotlin.Unit> onNextEpisode, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onEpisodeSelected, boolean hasPreviousEpisode, boolean hasNextEpisode) {
    }
    
    /**
     * 集数按钮
     */
    @androidx.compose.runtime.Composable()
    private static final void EpisodeButton(int episodeNumber, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}