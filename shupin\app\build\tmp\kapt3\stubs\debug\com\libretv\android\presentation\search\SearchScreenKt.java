package com.libretv.android.presentation.search;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u001e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a0\u0010\u0000\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u00a8\u0006\t"}, d2 = {"SearchScreen", "", "onVideoClick", "Lkotlin/Function1;", "Lcom/libretv/android/data/model/VideoItem;", "modifier", "Landroidx/compose/ui/Modifier;", "viewModel", "Lcom/libretv/android/presentation/search/SearchViewModel;", "app_debug"})
public final class SearchScreenKt {
    
    /**
     * 搜索页面
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SearchScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.libretv.android.data.model.VideoItem, kotlin.Unit> onVideoClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.libretv.android.presentation.search.SearchViewModel viewModel) {
    }
}