package com.libretv.android.data.repository;

/**
 * 视频数据仓库
 * 基于LibreTV原项目的聚合搜索机制
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u0002\n\u0000\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u001c\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ$\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u000b\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010\u0013J\u0018\u0010\u0014\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0016\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0017J \u0010\u0018\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0019\u001a\u00020\f2\u0006\u0010\u0011\u001a\u00020\u0012H\u0082@\u00a2\u0006\u0002\u0010\u001aJ \u0010\u001b\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0019\u001a\u00020\f2\u0006\u0010\u0011\u001a\u00020\u0012H\u0082@\u00a2\u0006\u0002\u0010\u001aJ\u001e\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\f0\u000f2\u0006\u0010\u001d\u001a\u00020\f2\u0006\u0010\u001e\u001a\u00020\fH\u0002J\u0010\u0010\u001f\u001a\u00020\f2\u0006\u0010\u001d\u001a\u00020\fH\u0002J\u0010\u0010 \u001a\u00020\f2\u0006\u0010\u001d\u001a\u00020\fH\u0002J\u0012\u0010!\u001a\u0004\u0018\u00010\f2\u0006\u0010\"\u001a\u00020\u0010H\u0002J\u0012\u0010#\u001a\u0004\u0018\u00010\u00122\u0006\u0010$\u001a\u00020\fH\u0002J\u001a\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000f0\tH\u0086@\u00a2\u0006\u0002\u0010&J\u0016\u0010\'\u001a\u00020(2\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006)"}, d2 = {"Lcom/libretv/android/data/repository/VideoRepository;", "", "apiService", "Lcom/libretv/android/data/api/VideoApiService;", "settingsRepository", "Lcom/libretv/android/data/repository/SettingsRepository;", "<init>", "(Lcom/libretv/android/data/api/VideoApiService;Lcom/libretv/android/data/repository/SettingsRepository;)V", "searchVideos", "Lkotlinx/coroutines/flow/Flow;", "Lcom/libretv/android/data/model/SearchState;", "keyword", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchSingleApi", "", "Lcom/libretv/android/data/model/VideoItem;", "apiConfig", "Lcom/libretv/android/data/api/ApiSource;", "(Lcom/libretv/android/data/api/ApiSource;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getVideoDetail", "Lcom/libretv/android/data/model/VideoDetail;", "videoItem", "(Lcom/libretv/android/data/model/VideoItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getVideoDetailFromApi", "vodId", "(Ljava/lang/String;Lcom/libretv/android/data/api/ApiSource;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getVideoDetailFromHtml", "extractM3U8LinksFromHtml", "html", "sourceCode", "extractTitleFromHtml", "extractDescriptionFromHtml", "extractPlayUrls", "item", "getApiConfigByCode", "code", "getSearchHistory", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveSearchHistory", "", "app_debug"})
public final class VideoRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.libretv.android.data.api.VideoApiService apiService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.libretv.android.data.repository.SettingsRepository settingsRepository = null;
    
    public VideoRepository(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.api.VideoApiService apiService, @org.jetbrains.annotations.NotNull()
    com.libretv.android.data.repository.SettingsRepository settingsRepository) {
        super();
    }
    
    /**
     * 搜索视频（聚合搜索）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object searchVideos(@org.jetbrains.annotations.NotNull()
    java.lang.String keyword, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.libretv.android.data.model.SearchState>> $completion) {
        return null;
    }
    
    /**
     * 搜索单个API源
     */
    private final java.lang.Object searchSingleApi(com.libretv.android.data.api.ApiSource apiConfig, java.lang.String keyword, kotlin.coroutines.Continuation<? super java.util.List<com.libretv.android.data.model.VideoItem>> $completion) {
        return null;
    }
    
    /**
     * 获取视频详情
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getVideoDetail(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.model.VideoItem videoItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.libretv.android.data.model.VideoDetail> $completion) {
        return null;
    }
    
    /**
     * 从API获取视频详情
     */
    private final java.lang.Object getVideoDetailFromApi(java.lang.String vodId, com.libretv.android.data.api.ApiSource apiConfig, kotlin.coroutines.Continuation<? super com.libretv.android.data.model.VideoDetail> $completion) {
        return null;
    }
    
    /**
     * 从HTML页面获取视频详情
     */
    private final java.lang.Object getVideoDetailFromHtml(java.lang.String vodId, com.libretv.android.data.api.ApiSource apiConfig, kotlin.coroutines.Continuation<? super com.libretv.android.data.model.VideoDetail> $completion) {
        return null;
    }
    
    /**
     * 从HTML中提取M3U8链接
     */
    private final java.util.List<java.lang.String> extractM3U8LinksFromHtml(java.lang.String html, java.lang.String sourceCode) {
        return null;
    }
    
    /**
     * 从HTML中提取标题
     */
    private final java.lang.String extractTitleFromHtml(java.lang.String html) {
        return null;
    }
    
    /**
     * 从HTML中提取描述
     */
    private final java.lang.String extractDescriptionFromHtml(java.lang.String html) {
        return null;
    }
    
    /**
     * 提取播放地址
     */
    private final java.lang.String extractPlayUrls(com.libretv.android.data.model.VideoItem item) {
        return null;
    }
    
    /**
     * 根据代码获取API配置
     */
    private final com.libretv.android.data.api.ApiSource getApiConfigByCode(java.lang.String code) {
        return null;
    }
    
    /**
     * 获取搜索历史
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSearchHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<java.lang.String>>> $completion) {
        return null;
    }
    
    /**
     * 保存搜索历史
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveSearchHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String keyword, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}