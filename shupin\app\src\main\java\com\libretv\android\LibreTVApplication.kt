package com.libretv.android

import android.app.Application
import com.libretv.android.di.appModules
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin
import org.koin.core.logger.Level

/**
 * LibreTV应用程序类
 */
class LibreTVApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化Koin依赖注入
        startKoin {
            androidLogger(Level.DEBUG)
            androidContext(this@LibreTVApplication)
            modules(appModules)
        }
    }
}
