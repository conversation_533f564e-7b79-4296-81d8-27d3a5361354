package com.libretv.android.presentation.search

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.libretv.android.data.model.SearchState
import com.libretv.android.data.model.VideoItem
import com.libretv.android.presentation.common.VideoCard
import org.koin.androidx.compose.koinViewModel

/**
 * 搜索页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchScreen(
    onVideoClick: (VideoItem) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: SearchViewModel = koinViewModel()
) {
    val searchState by viewModel.searchState.collectAsState()
    val searchHistory by viewModel.searchHistory.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    
    var localSearchQuery by remember { mutableStateOf("") }
    val keyboardController = LocalSoftwareKeyboardController.current
    
    // 同步本地查询状态
    LaunchedEffect(searchQuery) {
        localSearchQuery = searchQuery
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 搜索框
        SearchBar(
            query = localSearchQuery,
            onQueryChange = { 
                localSearchQuery = it
                viewModel.updateSearchQuery(it)
            },
            onSearch = { 
                viewModel.searchVideos(localSearchQuery)
                keyboardController?.hide()
            },
            placeholder = { Text("搜索你喜欢的视频...") },
            leadingIcon = {
                Icon(
                    Icons.Default.Search,
                    contentDescription = "搜索"
                )
            },
            trailingIcon = {
                if (localSearchQuery.isNotEmpty()) {
                    IconButton(
                        onClick = { 
                            localSearchQuery = ""
                            viewModel.updateSearchQuery("")
                            viewModel.clearSearchResults()
                        }
                    ) {
                        Icon(
                            Icons.Default.Clear,
                            contentDescription = "清除"
                        )
                    }
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            // SearchBar的内容区域可以放置搜索建议等
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 搜索历史
        if (searchHistory.isNotEmpty() && searchState is SearchState.Idle) {
            Text(
                text = "搜索历史",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                items(searchHistory) { keyword ->
                    FilterChip(
                        onClick = { 
                            viewModel.searchFromHistory(keyword)
                        },
                        label = { 
                            Text(
                                text = keyword,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        },
                        selected = false
                    )
                }
            }
        }
        
        // 搜索结果区域
        Box(modifier = Modifier.fillMaxSize()) {
            when (val state = searchState) {
                is SearchState.Idle -> {
                    // 空状态
                    Column(
                        modifier = Modifier.align(Alignment.Center),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.Search,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colorScheme.outline
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "输入关键词开始搜索",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.outline
                        )
                    }
                }
                
                is SearchState.Loading -> {
                    // 加载状态
                    Column(
                        modifier = Modifier.align(Alignment.Center),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator()
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "正在搜索...",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
                
                is SearchState.Success -> {
                    if (state.videos.isEmpty()) {
                        // 无结果状态
                        Column(
                            modifier = Modifier.align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "未找到相关视频",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.outline
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "尝试使用其他关键词",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.outline
                            )
                        }
                    } else {
                        // 搜索结果网格
                        LazyVerticalGrid(
                            columns = GridCells.Adaptive(minSize = 160.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp),
                            horizontalArrangement = Arrangement.spacedBy(12.dp),
                            contentPadding = PaddingValues(bottom = 16.dp)
                        ) {
                            items(state.videos) { video ->
                                VideoCard(
                                    video = video,
                                    onClick = { onVideoClick(video) }
                                )
                            }
                        }
                    }
                }
                
                is SearchState.Error -> {
                    // 错误状态
                    Column(
                        modifier = Modifier.align(Alignment.Center),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "搜索失败",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = state.message,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.outline
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { viewModel.retrySearch() }
                        ) {
                            Text("重试")
                        }
                    }
                }
            }
        }
    }
}
