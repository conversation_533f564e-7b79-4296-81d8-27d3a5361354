package com.libretv.android;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u0018\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a\u0014\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"LibreTVApp", "", "extractEpisodesFromVideo", "", "", "video", "Lcom/libretv/android/data/model/VideoItem;", "app_debug"})
public final class MainActivityKt {
    
    @androidx.compose.runtime.Composable()
    public static final void LibreTVApp() {
    }
    
    /**
     * 从视频项中提取播放集数
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<java.lang.String> extractEpisodesFromVideo(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.model.VideoItem video) {
        return null;
    }
}