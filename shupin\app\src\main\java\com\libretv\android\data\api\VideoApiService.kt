package com.libretv.android.data.api

import com.libretv.android.data.model.ApiResponse
import retrofit2.http.GET
import retrofit2.http.Query
import retrofit2.http.Url

/**
 * 视频API服务接口
 */
interface VideoApiService {
    
    /**
     * 搜索视频
     */
    @GET
    suspend fun searchVideos(
        @Url url: String,
        @Query("ac") action: String = "videolist",
        @Query("wd") keyword: String
    ): ApiResponse
    
    /**
     * 获取视频详情
     */
    @GET
    suspend fun getVideoDetail(
        @Url url: String,
        @Query("ac") action: String = "videolist",
        @Query("ids") ids: String
    ): ApiResponse
    
    /**
     * 分页搜索
     */
    @GET
    suspend fun searchVideosWithPage(
        @Url url: String,
        @Query("ac") action: String = "videolist",
        @Query("wd") keyword: String,
        @Query("pg") page: Int
    ): ApiResponse
}

    /**
     * 代理请求（获取HTML内容）
     */
    @GET
    suspend fun proxyRequest(@Url url: String): String
