package com.libretv.android.data.api;

/**
 * 自定义API配置
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/libretv/android/data/api/CustomApiConfig;", "", "<init>", "()V", "SEPARATOR", "", "MAX_SOURCES", "", "TEST_TIMEOUT_MS", "", "NAME_PREFIX", "VALIDATE_URL", "", "app_debug"})
public final class CustomApiConfig {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SEPARATOR = ",";
    public static final int MAX_SOURCES = 5;
    public static final long TEST_TIMEOUT_MS = 5000L;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String NAME_PREFIX = "Custom-";
    public static final boolean VALIDATE_URL = true;
    @org.jetbrains.annotations.NotNull()
    public static final com.libretv.android.data.api.CustomApiConfig INSTANCE = null;
    
    private CustomApiConfig() {
        super();
    }
}