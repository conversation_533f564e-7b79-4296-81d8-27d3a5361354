package com.libretv.android.data.api;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\u001a\u0018\u0010\u0000\u001a\u00020\u00012\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0087@\u00a2\u0006\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"proxyRequest", "", "url", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class VideoApiServiceKt {
    
    /**
     * 代理请求（获取HTML内容）
     */
    @retrofit2.http.GET()
    @org.jetbrains.annotations.Nullable()
    public static final java.lang.Object proxyRequest(@retrofit2.http.Url()
    @org.jetbrains.annotations.NotNull()
    java.lang.String url, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
}