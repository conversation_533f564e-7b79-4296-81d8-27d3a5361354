package com.libretv.android.utils

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.net.URLDecoder
import java.util.concurrent.TimeUnit

/**
 * 本地代理服务器
 * 用于处理跨域请求和广告过滤
 */
class LocalProxyServer(
    private val okHttpClient: OkHttpClient,
    private val adFilterInterceptor: AdFilterInterceptor
) {
    
    companion object {
        private const val PROXY_PORT = 8080
        private const val PROXY_HOST = "localhost"
    }
    
    /**
     * 处理代理请求
     */
    suspend fun handleProxyRequest(encodedUrl: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val targetUrl = URLDecoder.decode(encodedUrl, "UTF-8")
                
                val request = Request.Builder()
                    .url(targetUrl)
                    .addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36")
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                    .build()
                
                val response = okHttpClient.newCall(request).execute()
                val content = response.body?.string() ?: ""
                
                // 如果是M3U8文件，进行广告过滤
                if (isM3U8Content(targetUrl, response.header("Content-Type"))) {
                    adFilterInterceptor.filterM3U8Content(content)
                } else {
                    content
                }
            } catch (e: Exception) {
                throw Exception("代理请求失败: ${e.message}")
            }
        }
    }
    
    /**
     * 判断是否为M3U8内容
     */
    private fun isM3U8Content(url: String, contentType: String?): Boolean {
        return url.contains(".m3u8") || 
               contentType?.contains("mpegurl") == true ||
               contentType?.contains("m3u8") == true
    }
    
    /**
     * 获取代理URL
     */
    fun getProxyUrl(originalUrl: String): String {
        return "http://$PROXY_HOST:$PROXY_PORT/proxy/${java.net.URLEncoder.encode(originalUrl, "UTF-8")}"
    }
    
    /**
     * 启动代理服务器（简化版本，实际使用OkHttp拦截器）
     */
    fun start() {
        // 在实际实现中，这里会启动一个本地HTTP服务器
        // 但为了简化，我们直接在Repository层处理代理逻辑
    }
    
    /**
     * 停止代理服务器
     */
    fun stop() {
        // 停止本地HTTP服务器
    }
}
