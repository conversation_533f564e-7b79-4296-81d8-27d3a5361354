# LibreTV Android 使用说明

## 🎯 项目概述

LibreTV Android是基于原版LibreTV项目的Android原生应用，完全重写了核心功能，使用现代Android开发技术栈。

## 🚀 核心功能实现

### 1. 多源聚合搜索系统
```kotlin
// 支持的功能：
- 并行搜索多个API源
- 智能结果去重和合并
- 搜索历史记录管理
- 内容过滤（黄色内容过滤）
- 超时和错误处理
```

**实现位置**: `VideoRepository.searchVideos()`
- 使用Kotlin协程并行请求
- Flow响应式数据流
- 自动错误恢复机制

### 2. 广告过滤系统
```kotlin
// 基于原项目的M3U8广告过滤算法
- 检测EXTINF标签中的广告片段
- 移除DISCONTINUITY不连续标记
- 过滤广告关键词匹配的URL
- 支持自定义广告关键词配置
```

**实现位置**: `AdFilterInterceptor.filterM3U8Content()`
- OkHttp拦截器实现
- 实时M3U8内容过滤
- 保持播放流畅性

### 3. 官方ExoPlayer播放器
```kotlin
// ExoPlayer集成特性：
- HLS流媒体播放支持
- 自动缓冲和重试
- 播放进度记忆
- 多集连续播放
- 全屏播放控制
```

**实现位置**: `ExoPlayerComposable.kt`
- Compose UI集成
- 生命周期管理
- 播放状态监听

## 📱 界面功能说明

### 搜索界面 (SearchScreen)
- **搜索框**: 支持实时输入和回车搜索
- **搜索历史**: 显示最近5次搜索记录
- **结果展示**: 网格布局显示视频卡片
- **加载状态**: 搜索过程中显示加载动画
- **错误处理**: 网络错误时显示重试按钮

### 播放器界面 (PlayerScreen)
- **视频播放**: 全屏ExoPlayer播放器
- **控制界面**: 点击屏幕显示/隐藏控制栏
- **集数切换**: 支持上一集/下一集快速切换
- **集数选择**: 底部集数选择器
- **返回功能**: 左上角返回按钮

### 视频卡片 (VideoCard)
- **封面图片**: 异步加载，支持错误占位图
- **视频信息**: 标题、类型、年份、地区
- **来源标签**: 显示视频来源API
- **更新状态**: 显示更新进度或完结状态

## 🛠️ 技术架构详解

### MVVM + Repository 架构
```
View (Compose UI) 
    ↓
ViewModel (状态管理)
    ↓  
Repository (数据仓库)
    ↓
API Service (网络请求)
```

### 依赖注入 (Koin)
```kotlin
// 网络模块
val networkModule = module {
    single<OkHttpClient> { /* 配置 */ }
    single<Retrofit> { /* 配置 */ }
    single<VideoApiService> { /* 配置 */ }
}

// 数据模块  
val dataModule = module {
    single<VideoRepository> { /* 配置 */ }
    single<SettingsRepository> { /* 配置 */ }
}

// ViewModel模块
val viewModelModule = module {
    viewModel<SearchViewModel> { /* 配置 */ }
    viewModel<PlayerViewModel> { /* 配置 */ }
}
```

## 🔧 配置和自定义

### 1. API源配置
在 `ApiConfig.kt` 中添加新的API源：
```kotlin
val API_SOURCES = listOf(
    ApiSource(
        code = "custom_api",
        name = "自定义API",
        baseUrl = "https://your-api.com",
        searchPath = "/api.php/provide/vod/at/xml/?wd=",
        detailPath = "/api.php/provide/vod/at/xml/?ids="
    )
)
```

### 2. 广告过滤配置
在 `ApiConfig.kt` 中自定义广告关键词：
```kotlin
val AD_KEYWORDS = listOf(
    "ad", "ads", "advert", "commercial",
    "广告", "宣传", "推广"
)
```

### 3. 播放器配置
在 `ExoPlayerComposable.kt` 中调整播放器设置：
```kotlin
// 缓冲配置
val loadControl = DefaultLoadControl.Builder()
    .setBufferDurationsMs(
        minBufferMs = 15000,
        maxBufferMs = 50000,
        bufferForPlaybackMs = 2500,
        bufferForPlaybackAfterRebufferMs = 5000
    ).build()
```

## 📋 使用流程

### 1. 搜索视频
1. 打开应用进入搜索界面
2. 在搜索框输入关键词
3. 点击搜索或按回车键
4. 等待搜索结果加载
5. 浏览搜索结果列表

### 2. 播放视频
1. 点击视频卡片进入播放器
2. 自动开始播放第一集
3. 点击屏幕显示/隐藏控制界面
4. 使用底部集数选择器切换集数
5. 使用上一集/下一集按钮快速切换

### 3. 管理设置
- 搜索历史自动保存（最多5条）
- 播放进度自动记忆
- 广告过滤默认开启
- 内容过滤可在设置中调整

## 🐛 故障排除

### 常见问题
1. **搜索无结果**: 检查网络连接和API源配置
2. **播放失败**: 确认视频源URL有效性
3. **加载缓慢**: 检查网络速度和服务器响应
4. **应用崩溃**: 查看日志输出定位问题

### 调试方法
```kotlin
// 启用网络日志
HttpLoggingInterceptor().apply {
    level = HttpLoggingInterceptor.Level.BODY
}

// 查看播放器状态
exoPlayer.addListener(object : Player.Listener {
    override fun onPlayerError(error: PlaybackException) {
        Log.e("Player", "播放错误: ${error.message}")
    }
})
```

## 🔄 更新和维护

### 版本更新
- 定期更新依赖库版本
- 适配新的Android API
- 优化性能和用户体验

### 功能扩展
- 添加新的API源支持
- 增强播放器功能
- 改进用户界面设计

## 📞 技术支持

如遇到技术问题，请：
1. 查看项目文档和代码注释
2. 检查相关日志输出
3. 提交Issue描述问题详情
4. 提供复现步骤和环境信息
