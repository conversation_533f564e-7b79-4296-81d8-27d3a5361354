1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.libretv.android"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- 存储权限 -->
16    <uses-permission
16-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:10:22-78
18        android:maxSdkVersion="28" />
18-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:12:5-13:38
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:12:22-77
21        android:maxSdkVersion="32" />
21-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:13:9-35
22
23    <!-- 唤醒锁权限（用于视频播放） -->
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:16:5-68
24-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:16:22-65
25
26    <permission
26-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
27        android:name="com.libretv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.libretv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
31
32    <application
32-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:18:5-48:19
33        android:name="com.libretv.android.LibreTVApplication"
33-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:19:9-62
34        android:allowBackup="true"
34-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:20:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
36        android:dataExtractionRules="@xml/data_extraction_rules"
36-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:21:9-65
37        android:debuggable="true"
38        android:extractNativeLibs="false"
39        android:fullBackupContent="@xml/backup_rules"
39-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:22:9-54
40        android:icon="@mipmap/ic_launcher"
40-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:23:9-43
41        android:label="@string/app_name"
41-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:24:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:25:9-54
43        android:supportsRtl="true"
43-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:26:9-35
44        android:theme="@style/Theme.Shupin"
44-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:27:9-44
45        android:usesCleartextTraffic="true" >
45-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:28:9-44
46        <activity
46-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:31:9-40:20
47            android:name="com.libretv.android.MainActivity"
47-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:32:13-60
48            android:exported="true"
48-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:33:13-36
49            android:label="@string/app_name"
49-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:34:13-45
50            android:theme="@style/Theme.Shupin" >
50-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:35:13-48
51            <intent-filter>
51-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:36:13-39:29
52                <action android:name="android.intent.action.MAIN" />
52-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:37:17-69
52-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:37:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:38:17-77
54-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:38:27-74
55            </intent-filter>
56        </activity>
57        <activity
57-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:42:9-46:51
58            android:name="com.libretv.android.TestActivity"
58-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:43:13-60
59            android:exported="false"
59-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:44:13-37
60            android:label="LibreTV Test"
60-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:45:13-41
61            android:theme="@style/Theme.Shupin" />
61-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:46:13-48
62        <activity
62-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\e4eb87e8b4e057aa96ef521941878e87\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
63            android:name="androidx.compose.ui.tooling.PreviewActivity"
63-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\e4eb87e8b4e057aa96ef521941878e87\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
64            android:exported="true" />
64-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\e4eb87e8b4e057aa96ef521941878e87\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
65
66        <provider
66-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
67            android:name="androidx.startup.InitializationProvider"
67-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
68            android:authorities="com.libretv.android.androidx-startup"
68-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
69            android:exported="false" >
69-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
70            <meta-data
70-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.emoji2.text.EmojiCompatInitializer"
71-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
72                android:value="androidx.startup" />
72-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
73            <meta-data
73-->[androidx.lifecycle:lifecycle-process:2.9.1] D:\SDK\.gradle\caches\8.11.1\transforms\31b35701fcb4456abd4c32c490c5d3fc\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
74-->[androidx.lifecycle:lifecycle-process:2.9.1] D:\SDK\.gradle\caches\8.11.1\transforms\31b35701fcb4456abd4c32c490c5d3fc\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
75                android:value="androidx.startup" />
75-->[androidx.lifecycle:lifecycle-process:2.9.1] D:\SDK\.gradle\caches\8.11.1\transforms\31b35701fcb4456abd4c32c490c5d3fc\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
76            <meta-data
76-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
77-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
78                android:value="androidx.startup" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
79        </provider>
80
81        <activity
81-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4ec4b45658ea63447d165763d59c5634\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
82            android:name="androidx.activity.ComponentActivity"
82-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4ec4b45658ea63447d165763d59c5634\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
83            android:exported="true"
83-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4ec4b45658ea63447d165763d59c5634\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
84            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
84-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4ec4b45658ea63447d165763d59c5634\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
85
86        <service
86-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\148cf6cd9314c6c7ba9e2fd1793c170e\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
87            android:name="androidx.room.MultiInstanceInvalidationService"
87-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\148cf6cd9314c6c7ba9e2fd1793c170e\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
88            android:directBootAware="true"
88-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\148cf6cd9314c6c7ba9e2fd1793c170e\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
89            android:exported="false" />
89-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\148cf6cd9314c6c7ba9e2fd1793c170e\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
90
91        <receiver
91-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
92            android:name="androidx.profileinstaller.ProfileInstallReceiver"
92-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
93            android:directBootAware="false"
93-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
94            android:enabled="true"
94-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
95            android:exported="true"
95-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
96            android:permission="android.permission.DUMP" >
96-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
98                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
98-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
98-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
101                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
101-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
101-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
104                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
104-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
104-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
107                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
107-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
107-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
108            </intent-filter>
109        </receiver>
110    </application>
111
112</manifest>
