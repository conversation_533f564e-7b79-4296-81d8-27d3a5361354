1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.libretv.android"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- 存储权限 -->
16    <uses-permission
16-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:10:22-78
18        android:maxSdkVersion="28" />
18-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:12:5-13:38
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:12:22-77
21        android:maxSdkVersion="32" />
21-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:13:9-35
22
23    <!-- 唤醒锁权限（用于视频播放） -->
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:16:5-68
24-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:16:22-65
25
26    <permission
26-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
27        android:name="com.libretv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.libretv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
31
32    <application
32-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:18:5-44:19
33        android:name="com.libretv.android.LibreTVApplication"
33-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:19:9-62
34        android:allowBackup="true"
34-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:20:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\f85e049c328eb78e58d6027c269935a9\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
36        android:dataExtractionRules="@xml/data_extraction_rules"
36-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:21:9-65
37        android:debuggable="true"
38        android:extractNativeLibs="false"
39        android:fullBackupContent="@xml/backup_rules"
39-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:22:9-54
40        android:icon="@mipmap/ic_launcher"
40-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:23:9-43
41        android:label="@string/app_name"
41-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:24:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:25:9-54
43        android:supportsRtl="true"
43-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:26:9-35
44        android:testOnly="true"
45        android:theme="@style/Theme.Shupin"
45-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:27:9-44
46        android:usesCleartextTraffic="true" >
46-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:28:9-44
47        <activity
47-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:31:9-40:20
48            android:name="com.libretv.android.MainActivity"
48-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:32:13-60
49            android:exported="true"
49-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:33:13-36
50            android:label="@string/app_name"
50-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:34:13-45
51            android:theme="@style/Theme.Shupin" >
51-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:35:13-48
52            <intent-filter>
52-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:36:13-39:29
53                <action android:name="android.intent.action.MAIN" />
53-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:37:17-69
53-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:37:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:38:17-77
55-->D:\csxm\LibreTV\shupin\app\src\main\AndroidManifest.xml:38:27-74
56            </intent-filter>
57        </activity>
58        <activity
58-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\e4eb87e8b4e057aa96ef521941878e87\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
59            android:name="androidx.compose.ui.tooling.PreviewActivity"
59-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\e4eb87e8b4e057aa96ef521941878e87\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
60            android:exported="true" />
60-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\e4eb87e8b4e057aa96ef521941878e87\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
61
62        <provider
62-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
64            android:authorities="com.libretv.android.androidx-startup"
64-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\440bf670b7f75d241d54c52afc009fab\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.9.1] D:\SDK\.gradle\caches\8.11.1\transforms\31b35701fcb4456abd4c32c490c5d3fc\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.9.1] D:\SDK\.gradle\caches\8.11.1\transforms\31b35701fcb4456abd4c32c490c5d3fc\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.9.1] D:\SDK\.gradle\caches\8.11.1\transforms\31b35701fcb4456abd4c32c490c5d3fc\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <activity
77-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4ec4b45658ea63447d165763d59c5634\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
78            android:name="androidx.activity.ComponentActivity"
78-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4ec4b45658ea63447d165763d59c5634\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
79            android:exported="true"
79-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4ec4b45658ea63447d165763d59c5634\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
80            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
80-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4ec4b45658ea63447d165763d59c5634\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
81
82        <service
82-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\148cf6cd9314c6c7ba9e2fd1793c170e\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
83            android:name="androidx.room.MultiInstanceInvalidationService"
83-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\148cf6cd9314c6c7ba9e2fd1793c170e\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
84            android:directBootAware="true"
84-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\148cf6cd9314c6c7ba9e2fd1793c170e\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
85            android:exported="false" />
85-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\148cf6cd9314c6c7ba9e2fd1793c170e\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
86
87        <receiver
87-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
88            android:name="androidx.profileinstaller.ProfileInstallReceiver"
88-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
89            android:directBootAware="false"
89-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
90            android:enabled="true"
90-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
91            android:exported="true"
91-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
92            android:permission="android.permission.DUMP" >
92-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
94                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
94-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
97                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
97-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
97-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
100                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
100-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
103                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
103-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
103-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\106fa9e34b1d2afc19ae2c2c9432f140\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
104            </intent-filter>
105        </receiver>
106    </application>
107
108</manifest>
