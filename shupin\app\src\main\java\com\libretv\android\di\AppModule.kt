package com.libretv.android.di

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.libretv.android.data.api.VideoApiService
import com.libretv.android.data.repository.SettingsRepository
import com.libretv.android.data.repository.VideoRepository
import com.libretv.android.presentation.search.SearchViewModel
import com.libretv.android.utils.AdFilterInterceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * 网络模块
 */
val networkModule = module {
    
    // Gson
    single<Gson> {
        GsonBuilder()
            .setLenient()
            .create()
    }
    
    // HTTP日志拦截器
    single<HttpLoggingInterceptor> {
        HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
    }
    
    // 广告过滤拦截器
    single<AdFilterInterceptor> {
        AdFilterInterceptor()
    }
    
    // OkHttpClient
    single<OkHttpClient> {
        OkHttpClient.Builder()
            .addInterceptor(get<HttpLoggingInterceptor>())
            .addInterceptor(get<AdFilterInterceptor>())
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }
    
    // Retrofit
    single<Retrofit> {
        Retrofit.Builder()
            .baseUrl("https://api.example.com/") // 基础URL，实际使用时会被@Url覆盖
            .client(get())
            .addConverterFactory(GsonConverterFactory.create(get()))
            .build()
    }
    
    // API服务
    single<VideoApiService> {
        get<Retrofit>().create(VideoApiService::class.java)
    }
}

/**
 * 数据模块
 */
val dataModule = module {
    
    // SharedPreferences
    single<SharedPreferences> {
        androidContext().getSharedPreferences("libretv_prefs", Context.MODE_PRIVATE)
    }
    
    // 设置仓库
    single<SettingsRepository> {
        SettingsRepository(get())
    }
    
    // 视频仓库
    single<VideoRepository> {
        VideoRepository(get(), get())
    }
}

/**
 * ViewModel模块
 */
val viewModelModule = module {
    
    // 搜索ViewModel
    viewModel<SearchViewModel> {
        SearchViewModel(get())
    }
}

/**
 * 工具模块
 */
val utilsModule = module {
    
    // 这里可以添加其他工具类的依赖注入
}

/**
 * 应用模块列表
 */
val appModules = listOf(
    networkModule,
    dataModule,
    viewModelModule,
    utilsModule
)
