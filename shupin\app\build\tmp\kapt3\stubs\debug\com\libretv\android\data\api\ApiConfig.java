package com.libretv.android.data.api;

/**
 * API配置常量
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\"\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000eR\u0011\u0010\u0015\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u001d\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001c\u00a8\u0006\u001d"}, d2 = {"Lcom/libretv/android/data/api/ApiConfig;", "", "<init>", "()V", "PROXY_URL", "", "SEARCH_PATH", "DETAIL_PATH", "TIMEOUT_SECONDS", "", "API_SOURCES", "", "Lcom/libretv/android/data/api/ApiSource;", "getAPI_SOURCES", "()Ljava/util/List;", "DEFAULT_SELECTED_APIS", "", "getDEFAULT_SELECTED_APIS", "()Ljava/util/Set;", "AD_KEYWORDS", "getAD_KEYWORDS", "M3U8_PATTERN", "Lkotlin/text/Regex;", "getM3U8_PATTERN", "()Lkotlin/text/Regex;", "DEFAULT_HEADERS", "", "getDEFAULT_HEADERS", "()Ljava/util/Map;", "app_debug"})
public final class ApiConfig {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PROXY_URL = "http://localhost:8080/proxy/";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SEARCH_PATH = "?ac=videolist&wd=";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DETAIL_PATH = "?ac=videolist&ids=";
    public static final long TIMEOUT_SECONDS = 10L;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SEARCH_PATH = "?ac=videolist&wd=";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DETAIL_PATH = "?ac=videolist&ids=";
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.libretv.android.data.api.ApiSource> API_SOURCES = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Set<java.lang.String> DEFAULT_SELECTED_APIS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> AD_KEYWORDS = null;
    @org.jetbrains.annotations.NotNull()
    private static final kotlin.text.Regex M3U8_PATTERN = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Map<java.lang.String, java.lang.String> DEFAULT_HEADERS = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.libretv.android.data.api.ApiConfig INSTANCE = null;
    
    private ApiConfig() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.libretv.android.data.api.ApiSource> getAPI_SOURCES() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> getDEFAULT_SELECTED_APIS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAD_KEYWORDS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.text.Regex getM3U8_PATTERN() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> getDEFAULT_HEADERS() {
        return null;
    }
}