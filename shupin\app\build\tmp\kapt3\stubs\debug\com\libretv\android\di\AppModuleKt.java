package com.libretv.android.di;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010 \n\u0002\b\u0003\"\u0011\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\"\u0011\u0010\u0004\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0003\"\u0011\u0010\u0006\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0003\"\u0011\u0010\b\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0003\"\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u000e"}, d2 = {"networkModule", "Lorg/koin/core/module/Module;", "getNetworkModule", "()Lorg/koin/core/module/Module;", "dataModule", "getDataModule", "viewModelModule", "getViewModelModule", "utilsModule", "getUtilsModule", "appModules", "", "getAppModules", "()Ljava/util/List;", "app_debug"})
public final class AppModuleKt {
    
    /**
     * 网络模块
     */
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module networkModule = null;
    
    /**
     * 数据模块
     */
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module dataModule = null;
    
    /**
     * ViewModel模块
     */
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module viewModelModule = null;
    
    /**
     * 工具模块
     */
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module utilsModule = null;
    
    /**
     * 应用模块列表
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<org.koin.core.module.Module> appModules = null;
    
    /**
     * 网络模块
     */
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getNetworkModule() {
        return null;
    }
    
    /**
     * 数据模块
     */
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getDataModule() {
        return null;
    }
    
    /**
     * ViewModel模块
     */
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getViewModelModule() {
        return null;
    }
    
    /**
     * 工具模块
     */
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getUtilsModule() {
        return null;
    }
    
    /**
     * 应用模块列表
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<org.koin.core.module.Module> getAppModules() {
        return null;
    }
}