package com.libretv.android.data.api;

/**
 * 聚合搜索配置
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/libretv/android/data/api/AggregatedSearchConfig;", "", "<init>", "()V", "ENABLED", "", "TIMEOUT_MS", "", "MAX_RESULTS", "", "PARALLEL_REQUESTS", "SHOW_SOURCE_BADGES", "app_debug"})
public final class AggregatedSearchConfig {
    public static final boolean ENABLED = true;
    public static final long TIMEOUT_MS = 8000L;
    public static final int MAX_RESULTS = 10000;
    public static final boolean PARALLEL_REQUESTS = true;
    public static final boolean SHOW_SOURCE_BADGES = true;
    @org.jetbrains.annotations.NotNull()
    public static final com.libretv.android.data.api.AggregatedSearchConfig INSTANCE = null;
    
    private AggregatedSearchConfig() {
        super();
    }
}