package com.libretv.android.presentation.common;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a(\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u0012\u0010\b\u001a\u00020\u00012\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\f\u0010\t\u001a\u00020\u0007*\u00020\u0007H\u0007\u00a8\u0006\n"}, d2 = {"VideoCard", "", "video", "Lcom/libretv/android/data/model/VideoItem;", "onClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "VideoCardSkeleton", "shimmerEffect", "app_debug"})
public final class VideoCardKt {
    
    /**
     * 视频卡片组件
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void VideoCard(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.model.VideoItem video, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 视频卡片加载状态
     */
    @androidx.compose.runtime.Composable()
    public static final void VideoCardSkeleton(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 闪烁效果修饰符
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.Modifier shimmerEffect(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier $this$shimmerEffect) {
        return null;
    }
}