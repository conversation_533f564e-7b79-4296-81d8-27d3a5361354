package com.libretv.android

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.libretv.android.data.model.VideoItem
import com.libretv.android.ui.theme.LibreTVTheme

/**
 * 测试Activity - 用于验证基本功能
 */
class TestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            LibreTVTheme {
                TestScreen()
            }
        }
    }
}

@Composable
fun TestScreen() {
    var searchQuery by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(false) }
    var testResults by remember { mutableStateOf<List<VideoItem>>(emptyList()) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "LibreTV Android 测试",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 搜索框
        OutlinedTextField(
            value = searchQuery,
            onValueChange = { searchQuery = it },
            label = { Text("搜索关键词") },
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 测试按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = {
                    isLoading = true
                    // 模拟搜索
                    testResults = generateTestData(searchQuery)
                    isLoading = false
                },
                modifier = Modifier.weight(1f)
            ) {
                Text("模拟搜索")
            }
            
            Button(
                onClick = {
                    testResults = emptyList()
                },
                modifier = Modifier.weight(1f)
            ) {
                Text("清除结果")
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 结果显示
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (testResults.isNotEmpty()) {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(testResults) { video ->
                    TestVideoCard(video = video)
                }
            }
        } else {
            Text(
                text = "输入关键词并点击搜索测试",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.outline
            )
        }
    }
}

@Composable
fun TestVideoCard(video: VideoItem) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = video.vodName,
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "来源: ${video.sourceName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
                
                if (video.typeName != null) {
                    Text(
                        text = video.typeName,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.outline
                    )
                }
            }
            
            if (video.vodRemarks != null) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = video.vodRemarks,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.secondary
                )
            }
        }
    }
}

/**
 * 生成测试数据
 */
fun generateTestData(query: String): List<VideoItem> {
    if (query.isBlank()) return emptyList()
    
    return listOf(
        VideoItem(
            vodId = "1",
            vodName = "${query} - 测试视频1",
            vodPic = null,
            vodRemarks = "更新至第10集",
            typeName = "电视剧",
            vodYear = "2024",
            vodArea = "中国大陆",
            sourceName = "测试源1",
            sourceCode = "test1"
        ),
        VideoItem(
            vodId = "2",
            vodName = "${query} - 测试视频2",
            vodPic = null,
            vodRemarks = "HD高清",
            typeName = "电影",
            vodYear = "2023",
            vodArea = "美国",
            sourceName = "测试源2",
            sourceCode = "test2"
        ),
        VideoItem(
            vodId = "3",
            vodName = "${query} - 测试视频3",
            vodPic = null,
            vodRemarks = "完结",
            typeName = "动漫",
            vodYear = "2024",
            vodArea = "日本",
            sourceName = "测试源3",
            sourceCode = "test3"
        )
    )
}
