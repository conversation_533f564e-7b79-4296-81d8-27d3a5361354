package com.libretv.android.utils

import com.libretv.android.data.api.ApiConfig
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import java.net.URLEncoder

/**
 * 广告过滤拦截器
 * 基于LibreTV原项目的广告过滤机制
 */
class AdFilterInterceptor : Interceptor {
    
    companion object {
        private const val M3U8_CONTENT_TYPE = "application/vnd.apple.mpegurl"
        private const val DISCONTINUITY_TAG = "#EXT-X-DISCONTINUITY"
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)
        
        // 只处理M3U8文件
        if (isM3U8Content(request.url.toString(), response)) {
            val originalContent = response.body?.string() ?: ""
            val filteredContent = filterM3U8Content(originalContent)
            
            return response.newBuilder()
                .body(filteredContent.toResponseBody(M3U8_CONTENT_TYPE.toMediaType()))
                .build()
        }
        
        return response
    }
    
    /**
     * 判断是否为M3U8内容
     */
    private fun isM3U8Content(url: String, response: Response): Boolean {
        val contentType = response.header("Content-Type") ?: ""
        return url.contains(".m3u8") || 
               contentType.contains("mpegurl") ||
               contentType.contains("m3u8")
    }
    
    /**
     * 过滤M3U8内容中的广告片段
     * 基于LibreTV的filterAdsFromM3U8函数
     */
    fun filterM3U8Content(content: String): String {
        if (content.isEmpty()) return ""
        
        val lines = content.split("\n").toMutableList()
        val filteredLines = mutableListOf<String>()
        
        var i = 0
        while (i < lines.size) {
            val line = lines[i].trim()
            
            when {
                // 处理EXTINF标签（视频片段信息）
                line.startsWith("#EXTINF") -> {
                    val nextLine = if (i + 1 < lines.size) lines[i + 1].trim() else ""
                    
                    if (isAdSegment(nextLine)) {
                        // 跳过广告片段（EXTINF行和URL行）
                        i += 2
                        continue
                    } else {
                        // 保留非广告片段
                        filteredLines.add(line)
                        if (nextLine.isNotEmpty()) {
                            filteredLines.add(nextLine)
                            i += 2
                        } else {
                            i++
                        }
                    }
                }
                
                // 移除不连续标记（广告片段的分界标记）
                line.contains(DISCONTINUITY_TAG) -> {
                    i++
                }
                
                // 保留其他行
                else -> {
                    filteredLines.add(line)
                    i++
                }
            }
        }
        
        return filteredLines.joinToString("\n")
    }
    
    /**
     * 判断是否为广告片段
     */
    private fun isAdSegment(url: String): Boolean {
        if (url.isEmpty()) return false
        
        val lowerUrl = url.lowercase()
        return ApiConfig.AD_KEYWORDS.any { keyword ->
            lowerUrl.contains(keyword)
        }
    }
    
    /**
     * 处理M3U8 URL，添加代理前缀
     */
    fun processM3U8Url(originalUrl: String): String {
        return if (originalUrl.contains(".m3u8")) {
            "${ApiConfig.PROXY_URL}${URLEncoder.encode(originalUrl, "UTF-8")}"
        } else {
            originalUrl
        }
    }
    
    /**
     * 递归处理M3U8内容
     */
    suspend fun processM3U8Content(targetUrl: String, content: String): String {
        // 判断是主列表还是媒体列表
        return if (content.contains("#EXT-X-STREAM-INF") || content.contains("#EXT-X-MEDIA:")) {
            processMasterPlaylist(targetUrl, content)
        } else {
            processMediaPlaylist(targetUrl, content)
        }
    }
    
    /**
     * 处理主播放列表
     */
    private suspend fun processMasterPlaylist(url: String, content: String): String {
        val baseUrl = getBaseUrl(url)
        val lines = content.split("\n")
        var highestBandwidth = -1
        var bestVariantUrl = ""
        
        // 查找最高带宽的流
        for (i in lines.indices) {
            if (lines[i].startsWith("#EXT-X-STREAM-INF")) {
                val bandwidthMatch = Regex("BANDWIDTH=(\\d+)").find(lines[i])
                val currentBandwidth = bandwidthMatch?.groupValues?.get(1)?.toIntOrNull() ?: 0
                
                // 找到下一行的URI
                for (j in i + 1 until lines.size) {
                    val line = lines[j].trim()
                    if (line.isNotEmpty() && !line.startsWith("#")) {
                        if (currentBandwidth >= highestBandwidth) {
                            highestBandwidth = currentBandwidth
                            bestVariantUrl = resolveUrl(baseUrl, line)
                        }
                        break
                    }
                }
            }
        }
        
        // 如果找到了最佳变体，返回处理后的内容
        return if (bestVariantUrl.isNotEmpty()) {
            // 这里应该递归获取子播放列表内容，简化处理
            processMediaPlaylist(bestVariantUrl, content)
        } else {
            processMediaPlaylist(url, content)
        }
    }
    
    /**
     * 处理媒体播放列表
     */
    private fun processMediaPlaylist(url: String, content: String): String {
        val baseUrl = getBaseUrl(url)
        val lines = content.split("\n")
        val output = mutableListOf<String>()
        
        for (i in lines.indices) {
            val line = lines[i].trim()
            
            // 保留最后一个空行
            if (line.isEmpty() && i == lines.size - 1) {
                output.add(line)
                continue
            }
            if (line.isEmpty()) continue
            
            when {
                line.startsWith("#EXT-X-KEY") -> {
                    output.add(processKeyLine(line, baseUrl))
                }
                line.startsWith("#EXT-X-MAP") -> {
                    output.add(processMapLine(line, baseUrl))
                }
                line.startsWith("#EXTINF") -> {
                    output.add(line)
                }
                !line.startsWith("#") -> {
                    // 处理URL行
                    val absoluteUrl = resolveUrl(baseUrl, line)
                    output.add(rewriteUrlToProxy(absoluteUrl))
                }
                else -> {
                    // 保留其他M3U8标签
                    output.add(line)
                }
            }
        }
        
        return output.joinToString("\n")
    }
    
    /**
     * 获取基础URL
     */
    private fun getBaseUrl(url: String): String {
        return try {
            val lastSlashIndex = url.lastIndexOf('/')
            if (lastSlashIndex > 0) {
                url.substring(0, lastSlashIndex + 1)
            } else {
                url
            }
        } catch (e: Exception) {
            url
        }
    }
    
    /**
     * 解析相对URL为绝对URL
     */
    private fun resolveUrl(baseUrl: String, relativeUrl: String): String {
        return if (relativeUrl.startsWith("http")) {
            relativeUrl
        } else {
            baseUrl + relativeUrl
        }
    }
    
    /**
     * 重写URL为代理URL
     */
    private fun rewriteUrlToProxy(url: String): String {
        return "${ApiConfig.PROXY_URL}${URLEncoder.encode(url, "UTF-8")}"
    }
    
    /**
     * 处理KEY行
     */
    private fun processKeyLine(line: String, baseUrl: String): String {
        return line.replace(Regex("URI=\"([^\"]+)\"")) { matchResult ->
            val uri = matchResult.groupValues[1]
            val absoluteUri = resolveUrl(baseUrl, uri)
            "URI=\"${rewriteUrlToProxy(absoluteUri)}\""
        }
    }
    
    /**
     * 处理MAP行
     */
    private fun processMapLine(line: String, baseUrl: String): String {
        return line.replace(Regex("URI=\"([^\"]+)\"")) { matchResult ->
            val uri = matchResult.groupValues[1]
            val absoluteUri = resolveUrl(baseUrl, uri)
            "URI=\"${rewriteUrlToProxy(absoluteUri)}\""
        }
    }
}
