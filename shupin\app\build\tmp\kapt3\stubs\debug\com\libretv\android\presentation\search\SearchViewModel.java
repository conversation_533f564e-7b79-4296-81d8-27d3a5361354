package com.libretv.android.presentation.search;

/**
 * 搜索页面ViewModel
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u000e\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u000fJ\u000e\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u000fJ\u0006\u0010\u001a\u001a\u00020\u0016J\u0006\u0010\u001b\u001a\u00020\u0016J\b\u0010\u001c\u001a\u00020\u0016H\u0002J\u000e\u0010\u001d\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u000fJ\u0006\u0010\u001e\u001a\u00020\u0016J\u000e\u0010\u001f\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u000fJ\u0006\u0010 \u001a\u00020!J\u0006\u0010\"\u001a\u00020#J\u0006\u0010$\u001a\u00020#R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u001a\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\fR\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000f0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\f\u00a8\u0006%"}, d2 = {"Lcom/libretv/android/presentation/search/SearchViewModel;", "Landroidx/lifecycle/ViewModel;", "videoRepository", "Lcom/libretv/android/data/repository/VideoRepository;", "<init>", "(Lcom/libretv/android/data/repository/VideoRepository;)V", "_searchState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/libretv/android/data/model/SearchState;", "searchState", "Lkotlinx/coroutines/flow/StateFlow;", "getSearchState", "()Lkotlinx/coroutines/flow/StateFlow;", "_searchHistory", "", "", "searchHistory", "getSearchHistory", "_searchQuery", "searchQuery", "getSearchQuery", "searchVideos", "", "keyword", "updateSearchQuery", "query", "clearSearchResults", "retrySearch", "loadSearchHistory", "removeSearchHistoryItem", "clearSearchHistory", "searchFromHistory", "getSearchResultCount", "", "hasSearchResults", "", "isSearching", "app_debug"})
public final class SearchViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.libretv.android.data.repository.VideoRepository videoRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.libretv.android.data.model.SearchState> _searchState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.libretv.android.data.model.SearchState> searchState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<java.lang.String>> _searchHistory = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<java.lang.String>> searchHistory = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _searchQuery = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> searchQuery = null;
    
    public SearchViewModel(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.repository.VideoRepository videoRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.libretv.android.data.model.SearchState> getSearchState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<java.lang.String>> getSearchHistory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getSearchQuery() {
        return null;
    }
    
    /**
     * 搜索视频
     */
    public final void searchVideos(@org.jetbrains.annotations.NotNull()
    java.lang.String keyword) {
    }
    
    /**
     * 更新搜索查询
     */
    public final void updateSearchQuery(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
    
    /**
     * 清除搜索结果
     */
    public final void clearSearchResults() {
    }
    
    /**
     * 重试搜索
     */
    public final void retrySearch() {
    }
    
    /**
     * 加载搜索历史
     */
    private final void loadSearchHistory() {
    }
    
    /**
     * 删除搜索历史项
     */
    public final void removeSearchHistoryItem(@org.jetbrains.annotations.NotNull()
    java.lang.String keyword) {
    }
    
    /**
     * 清除所有搜索历史
     */
    public final void clearSearchHistory() {
    }
    
    /**
     * 从历史记录搜索
     */
    public final void searchFromHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String keyword) {
    }
    
    /**
     * 获取当前搜索结果数量
     */
    public final int getSearchResultCount() {
        return 0;
    }
    
    /**
     * 检查是否有搜索结果
     */
    public final boolean hasSearchResults() {
        return false;
    }
    
    /**
     * 检查是否正在搜索
     */
    public final boolean isSearching() {
        return false;
    }
}