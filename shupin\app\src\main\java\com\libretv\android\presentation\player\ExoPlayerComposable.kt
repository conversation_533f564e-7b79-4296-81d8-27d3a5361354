package com.libretv.android.presentation.player

import android.content.Context
import android.view.ViewGroup
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.source.hls.HlsMediaSource
import com.google.android.exoplayer2.ui.PlayerView
import com.google.android.exoplayer2.upstream.DefaultHttpDataSource
import com.google.android.exoplayer2.util.MimeTypes
import com.libretv.android.data.model.PlayerState
import com.libretv.android.data.model.VideoPlayInfo
import kotlinx.coroutines.delay

/**
 * ExoPlayer Compose组件
 * 使用官方ExoPlayer实现视频播放
 */
@Composable
fun ExoPlayerComposable(
    playInfo: VideoPlayInfo,
    onPlayerStateChanged: (PlayerState) -> Unit,
    onPositionChanged: (Long) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    
    // 创建ExoPlayer实例
    val exoPlayer = remember {
        createExoPlayer(context, onPlayerStateChanged, onPositionChanged)
    }
    
    // 创建PlayerView
    val playerView = remember {
        PlayerView(context).apply {
            player = exoPlayer
            useController = true
            setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        }
    }
    
    // 监听播放信息变化
    LaunchedEffect(playInfo.currentEpisodeIndex) {
        val currentEpisode = playInfo.episodes.getOrNull(playInfo.currentEpisodeIndex)
        if (currentEpisode != null) {
            loadVideo(exoPlayer, currentEpisode, playInfo.lastPlayPosition)
        }
    }
    
    // 生命周期管理
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    exoPlayer.pause()
                }
                Lifecycle.Event.ON_RESUME -> {
                    if (exoPlayer.playWhenReady) {
                        exoPlayer.play()
                    }
                }
                Lifecycle.Event.ON_DESTROY -> {
                    exoPlayer.release()
                }
                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
            exoPlayer.release()
        }
    }
    
    // 定期更新播放位置
    LaunchedEffect(exoPlayer) {
        while (true) {
            delay(1000)
            if (exoPlayer.isPlaying) {
                onPositionChanged(exoPlayer.currentPosition)
            }
        }
    }
    
    AndroidView(
        factory = { playerView },
        modifier = modifier.fillMaxSize()
    )
}

/**
 * 创建ExoPlayer实例
 */
private fun createExoPlayer(
    context: Context,
    onPlayerStateChanged: (PlayerState) -> Unit,
    onPositionChanged: (Long) -> Unit
): ExoPlayer {
    return ExoPlayer.Builder(context)
        .build()
        .apply {
            // 添加播放器监听器
            addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    when (playbackState) {
                        Player.STATE_BUFFERING -> {
                            onPlayerStateChanged(PlayerState.Loading)
                        }
                        Player.STATE_READY -> {
                            onPlayerStateChanged(PlayerState.Ready)
                        }
                        Player.STATE_ENDED -> {
                            onPlayerStateChanged(PlayerState.Ready)
                        }
                        Player.STATE_IDLE -> {
                            onPlayerStateChanged(PlayerState.Ready)
                        }
                    }
                }
                
                override fun onIsPlayingChanged(isPlaying: Boolean) {
                    if (isPlaying) {
                        onPlayerStateChanged(PlayerState.Playing)
                    } else {
                        onPlayerStateChanged(PlayerState.Paused)
                    }
                }
                
                override fun onPlayerError(error: com.google.android.exoplayer2.PlaybackException) {
                    onPlayerStateChanged(PlayerState.Error(error.message ?: "播放错误"))
                }
            })
            
            // 设置播放参数
            playWhenReady = true
        }
}

/**
 * 加载视频
 */
private fun loadVideo(exoPlayer: ExoPlayer, videoUrl: String, startPosition: Long = 0L) {
    try {
        // 创建HLS数据源工厂
        val dataSourceFactory = DefaultHttpDataSource.Factory()
            .setUserAgent("LibreTV-Android/1.0")
            .setConnectTimeoutMs(10000)
            .setReadTimeoutMs(10000)
        
        // 创建HLS媒体源
        val hlsMediaSource = HlsMediaSource.Factory(dataSourceFactory)
            .createMediaSource(MediaItem.fromUri(videoUrl))
        
        // 设置媒体源
        exoPlayer.setMediaSource(hlsMediaSource)
        
        // 准备播放器
        exoPlayer.prepare()
        
        // 如果有起始位置，跳转到指定位置
        if (startPosition > 0) {
            exoPlayer.seekTo(startPosition)
        }
        
        // 开始播放
        exoPlayer.play()
        
    } catch (e: Exception) {
        // 如果HLS加载失败，尝试直接加载
        val mediaItem = MediaItem.Builder()
            .setUri(videoUrl)
            .setMimeType(MimeTypes.APPLICATION_M3U8)
            .build()
            
        exoPlayer.setMediaItem(mediaItem)
        exoPlayer.prepare()
        
        if (startPosition > 0) {
            exoPlayer.seekTo(startPosition)
        }
        
        exoPlayer.play()
    }
}
