<!DOCTYPE html>
<html lang="zh">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>关于我们 - LibreTV</title>
	<script src="libs/tailwindcss.min.js"></script>
	<link rel="stylesheet" href="css/styles.css">
	<link rel="manifest" href="manifest.json">

</head>
<body class="page-bg text-white">
	<div class="container mx-auto px-4 py-8">
		<header class="text-center mb-8">
			<h1 class="text-5xl font-bold gradient-text">关于我们</h1>
		</header>
		<main class="text-center">
			<p class="text-gray-300 mb-4">
				本项目代码托管在 GitHub 上，欢迎访问我们的仓库：
				<a href="https://github.com/LibreSpark/LibreTV" class="text-blue-400 hover:underline" target="_blank" rel="noopener">https://github.com/LibreSpark/LibreTV</a>
			</p>
			<p class="text-gray-300 mb-8">
				LibreTV 是一个免费的在线视频搜索平台，提供视频搜索和播放服务，致力于为用户带来最佳体验。
			</p>
			
			<!-- 版权声明与投诉机制 -->
			<div class="mt-12 mb-8 max-w-2xl mx-auto border border-gray-700 rounded-lg p-6 bg-[#111]">
				<h2 class="text-xl font-semibold mb-4 text-blue-400">版权声明与投诉机制</h2>
				<p class="text-gray-300 mb-4 text-left">
					LibreTV 仅提供视频搜索服务，不直接提供、存储或上传任何视频内容。所有搜索结果均来自第三方公开接口。用户在使用本站服务时，须遵守相关法律法规，不得利用搜索结果从事侵权行为，如下载、传播未经授权的作品等。
				</p>
				<p class="text-gray-300 mb-4 text-left">
					若您是版权方或相关权利人，发现本站搜索结果中存在侵犯您合法权益的内容，请通过以下渠道向我们反馈：
				</p>
				<div class="bg-[#1a1a1a] p-4 rounded-md mb-4 text-left">
					<p class="text-gray-300"><span class="font-semibold">投诉邮箱：</span><a href="mailto:<EMAIL>" class="text-blue-400 hover:underline"><EMAIL></a></p>
				</div>
				<p class="text-gray-300 text-left">
					请在投诉邮件中提供：您的身份证明、权利证明、侵权内容的具体链接及相关说明。我们将在收到投诉后尽快处理，对于确认侵权的内容，将立即断开相关链接，停止展示侵权内容，并将处理结果反馈给您。
				</p>
			</div>
		</main>
		<footer class="mt-12 text-center">
			<div class="flex justify-center space-x-4">
				<a href="/" class="text-gray-400 hover:text-white transition-colors">回到首页</a>
				<a href="https://www.msf.hk/zh-hant/donate/general?type=one-off" target="_blank" rel="noopener" class="text-blue-400 hover:text-blue-300 transition-colors">捐赠</a>
			</div>
		</footer>
	</div>
</body>
</html>
