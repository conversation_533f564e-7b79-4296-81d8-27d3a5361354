package com.libretv.android.data.repository;

/**
 * 设置数据仓库
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010$\n\u0002\b\u0003\u0018\u0000  2\u00020\u0001:\u0001 B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007J\u0014\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fJ\u0006\u0010\u000e\u001a\u00020\u000fJ\u000e\u0010\u0010\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\u000fJ\u0006\u0010\u0012\u001a\u00020\u000fJ\u000e\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\u000fJ\u0006\u0010\u0014\u001a\u00020\u000fJ\u000e\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\u000fJ\u0012\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00070\u0017J\u000e\u0010\u0018\u001a\u00020\n2\u0006\u0010\u0019\u001a\u00020\rJ\u0006\u0010\u001a\u001a\u00020\nJ\u000e\u0010\u001b\u001a\u00020\n2\u0006\u0010\u0019\u001a\u00020\rJ\u000e\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\r0\u0007H\u0002J\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u001eJ\u0006\u0010\u001f\u001a\u00020\nR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006!"}, d2 = {"Lcom/libretv/android/data/repository/SettingsRepository;", "", "sharedPreferences", "Landroid/content/SharedPreferences;", "<init>", "(Landroid/content/SharedPreferences;)V", "getSelectedApis", "", "Lcom/libretv/android/data/api/ApiSource;", "setSelectedApis", "", "apiCodes", "", "", "isYellowFilterEnabled", "", "setYellowFilterEnabled", "enabled", "isAdFilteringEnabled", "setAdFilteringEnabled", "isAutoplayEnabled", "setAutoplayEnabled", "getSearchHistory", "Lkotlinx/coroutines/flow/Flow;", "saveSearchHistory", "keyword", "clearSearchHistory", "removeSearchHistory", "getSearchHistorySync", "getSettingsSummary", "", "resetToDefaults", "Companion", "app_debug"})
public final class SettingsRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences sharedPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_SELECTED_APIS = "selected_apis";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_YELLOW_FILTER_ENABLED = "yellow_filter_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_AD_FILTERING_ENABLED = "ad_filtering_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_SEARCH_HISTORY = "search_history";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_AUTOPLAY_ENABLED = "autoplay_enabled";
    private static final int MAX_SEARCH_HISTORY = 5;
    @org.jetbrains.annotations.NotNull()
    public static final com.libretv.android.data.repository.SettingsRepository.Companion Companion = null;
    
    public SettingsRepository(@org.jetbrains.annotations.NotNull()
    android.content.SharedPreferences sharedPreferences) {
        super();
    }
    
    /**
     * 获取选中的API源
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.libretv.android.data.api.ApiSource> getSelectedApis() {
        return null;
    }
    
    /**
     * 设置选中的API源
     */
    public final void setSelectedApis(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> apiCodes) {
    }
    
    /**
     * 是否启用黄色内容过滤
     */
    public final boolean isYellowFilterEnabled() {
        return false;
    }
    
    /**
     * 设置黄色内容过滤
     */
    public final void setYellowFilterEnabled(boolean enabled) {
    }
    
    /**
     * 是否启用广告过滤
     */
    public final boolean isAdFilteringEnabled() {
        return false;
    }
    
    /**
     * 设置广告过滤
     */
    public final void setAdFilteringEnabled(boolean enabled) {
    }
    
    /**
     * 是否启用自动播放
     */
    public final boolean isAutoplayEnabled() {
        return false;
    }
    
    /**
     * 设置自动播放
     */
    public final void setAutoplayEnabled(boolean enabled) {
    }
    
    /**
     * 获取搜索历史
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<java.lang.String>> getSearchHistory() {
        return null;
    }
    
    /**
     * 保存搜索历史
     */
    public final void saveSearchHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String keyword) {
    }
    
    /**
     * 清除搜索历史
     */
    public final void clearSearchHistory() {
    }
    
    /**
     * 删除单个搜索历史
     */
    public final void removeSearchHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String keyword) {
    }
    
    /**
     * 同步获取搜索历史
     */
    private final java.util.List<java.lang.String> getSearchHistorySync() {
        return null;
    }
    
    /**
     * 获取所有设置的摘要
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getSettingsSummary() {
        return null;
    }
    
    /**
     * 重置所有设置为默认值
     */
    public final void resetToDefaults() {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/libretv/android/data/repository/SettingsRepository$Companion;", "", "<init>", "()V", "KEY_SELECTED_APIS", "", "KEY_YELLOW_FILTER_ENABLED", "KEY_AD_FILTERING_ENABLED", "KEY_SEARCH_HISTORY", "KEY_AUTOPLAY_ENABLED", "MAX_SEARCH_HISTORY", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}