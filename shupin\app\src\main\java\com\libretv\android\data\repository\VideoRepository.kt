package com.libretv.android.data.repository

import com.libretv.android.data.api.ApiConfig
import com.libretv.android.data.api.ApiSource
import com.libretv.android.data.api.VideoApiService
import com.libretv.android.data.model.SearchState
import com.libretv.android.data.model.VideoDetail
import com.libretv.android.data.model.VideoItem
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.net.URLEncoder

/**
 * 视频数据仓库
 * 基于LibreTV原项目的聚合搜索机制
 */
class VideoRepository(
    private val apiService: VideoApiService,
    private val settingsRepository: SettingsRepository
) {
    
    /**
     * 搜索视频（聚合搜索）
     */
    suspend fun searchVideos(keyword: String): Flow<SearchState> = flow {
        emit(SearchState.Loading)
        
        try {
            val selectedApis = settingsRepository.getSelectedApis()
            val yellowFilterEnabled = settingsRepository.isYellowFilterEnabled()
            
            // 并行搜索所有API源
            val searchResults = coroutineScope {
                selectedApis.map { apiConfig ->
                    async {
                        searchSingleApi(apiConfig, keyword)
                    }
                }
            }.awaitAll()
            
            // 合并所有结果
            val allResults = searchResults.flatten()
            
            // 内容过滤
            val filteredResults = if (yellowFilterEnabled) {
                allResults.filter { item ->
                    !ApiConfig.AD_KEYWORDS.any { keyword ->
                        item.typeName?.contains(keyword, ignoreCase = true) == true
                    }
                }
            } else {
                allResults
            }
            
            // 去重和排序
            val uniqueResults = filteredResults
                .distinctBy { "${it.sourceCode}_${it.vodId}" }
                .sortedBy { it.vodName }
            
            emit(SearchState.Success(uniqueResults))
            
        } catch (e: Exception) {
            emit(SearchState.Error(e.message ?: "搜索失败"))
        }
    }
    
    /**
     * 搜索单个API源
     */
    private suspend fun searchSingleApi(apiConfig: ApiSource, keyword: String): List<VideoItem> {
        return try {
            val encodedKeyword = URLEncoder.encode(keyword, "UTF-8")
            val url = "${apiConfig.baseUrl}${ApiConfig.SEARCH_PATH}$encodedKeyword"

            val response = apiService.searchVideos(url, keyword = keyword)
            
            // 为结果添加源信息
            response.list.map { item ->
                item.copy(
                    sourceName = apiConfig.name,
                    sourceCode = apiConfig.code
                )
            }
        } catch (e: Exception) {
            // 单个API失败不影响整体搜索
            emptyList()
        }
    }
    
    /**
     * 获取视频详情
     */
    suspend fun getVideoDetail(videoItem: VideoItem): VideoDetail? {
        return try {
            val apiConfig = getApiConfigByCode(videoItem.sourceCode) ?: return null
            
            // 如果有特殊详情URL，使用HTML解析方式
            if (apiConfig.detailUrl != null) {
                getVideoDetailFromHtml(videoItem.vodId, apiConfig)
            } else {
                // 使用标准API方式
                getVideoDetailFromApi(videoItem.vodId, apiConfig)
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 从API获取视频详情
     */
    private suspend fun getVideoDetailFromApi(vodId: String, apiConfig: ApiSource): VideoDetail? {
        return try {
            val url = "${apiConfig.baseUrl}${ApiConfig.DETAIL_PATH}$vodId"
            val response = apiService.getVideoDetail(url, ids = vodId)
            
            response.list.firstOrNull()?.let { item ->
                VideoDetail(
                    vodId = item.vodId,
                    vodName = item.vodName,
                    vodPic = item.vodPic,
                    vodContent = item.vodContent,
                    vodPlayUrl = extractPlayUrls(item),
                    typeName = item.typeName,
                    vodYear = item.vodYear,
                    vodArea = item.vodArea,
                    vodDirector = item.vodDirector,
                    vodActor = item.vodActor,
                    vodRemarks = item.vodRemarks,
                    sourceName = apiConfig.name,
                    sourceCode = apiConfig.code
                )
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 从HTML页面获取视频详情
     */
    private suspend fun getVideoDetailFromHtml(vodId: String, apiConfig: ApiSource): VideoDetail? {
        return try {
            val detailUrl = "${apiConfig.detailUrl}/index.php/vod/detail/id/$vodId.html"
            // 暂时返回null，等待实现HTML解析功能
            null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 从HTML中提取M3U8链接
     */
    private fun extractM3U8LinksFromHtml(html: String, sourceCode: String): List<String> {
        val matches = mutableListOf<String>()
        
        // 根据不同源使用不同的正则表达式
        val pattern = when (sourceCode) {
            "ffzy" -> Regex("\\$(https?://[^\"'\\s]+?/\\d{8}/\\d+_[a-f0-9]+/index\\.m3u8)")
            else -> Regex("\\$(https?://[^\"'\\s]+?\\.m3u8)")
        }
        
        pattern.findAll(html).forEach { matchResult ->
            var link = matchResult.groupValues[1]
            // 移除可能的括号内容
            val parenIndex = link.indexOf('(')
            if (parenIndex > 0) {
                link = link.substring(0, parenIndex)
            }
            matches.add(link)
        }
        
        // 去重
        return matches.distinct()
    }
    
    /**
     * 从HTML中提取标题
     */
    private fun extractTitleFromHtml(html: String): String {
        val titleMatch = Regex("<h1[^>]*>([^<]+)</h1>").find(html)
        return titleMatch?.groupValues?.get(1)?.trim() ?: ""
    }
    
    /**
     * 从HTML中提取描述
     */
    private fun extractDescriptionFromHtml(html: String): String {
        val descMatch = Regex("<div[^>]*class=[\"']sketch[\"'][^>]*>([\\s\\S]*?)</div>").find(html)
        return descMatch?.groupValues?.get(1)?.replace(Regex("<[^>]+>"), " ")?.trim() ?: ""
    }
    
    /**
     * 提取播放地址
     */
    private fun extractPlayUrls(item: VideoItem): String? {
        // 这里应该解析vod_play_url字段，提取实际的播放链接
        // 由于VideoItem中没有vod_play_url字段，这里返回null
        // 实际实现中需要从API响应中获取完整的播放信息
        return null
    }
    
    /**
     * 根据代码获取API配置
     */
    private fun getApiConfigByCode(code: String): ApiSource? {
        return ApiConfig.API_SOURCES.find { it.code == code }
    }
    
    /**
     * 获取搜索历史
     */
    suspend fun getSearchHistory(): Flow<List<String>> {
        return settingsRepository.getSearchHistory()
    }
    
    /**
     * 保存搜索历史
     */
    suspend fun saveSearchHistory(keyword: String) {
        settingsRepository.saveSearchHistory(keyword)
    }
}
