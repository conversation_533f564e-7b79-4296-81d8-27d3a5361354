package com.libretv.android

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.unit.dp
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.libretv.android.data.model.VideoItem
import com.libretv.android.data.model.VideoPlayInfo
import com.libretv.android.presentation.search.SearchScreen
import com.libretv.android.presentation.player.PlayerScreen
import com.libretv.android.ui.theme.LibreTVTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LibreTVTheme {
                // 暂时使用简单测试，验证基本功能
                SimpleTestScreen()
            }
        }
    }
}

@Composable
fun LibreTVApp() {
    val navController = rememberNavController()
    var selectedVideo by remember { mutableStateOf<VideoItem?>(null) }

    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = "search",
            modifier = Modifier.padding(innerPadding)
        ) {
            // 搜索页面
            composable("search") {
                SearchScreen(
                    onVideoClick = { video ->
                        selectedVideo = video
                        navController.navigate("player")
                    }
                )
            }

            // 播放器页面
            composable("player") {
                selectedVideo?.let { video ->
                    // 创建测试播放信息
                    val playInfo = VideoPlayInfo(
                        videoId = video.vodId,
                        title = video.vodName,
                        episodes = listOf(
                            // 测试用的HLS流地址
                            "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8",
                            "https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8"
                        ),
                        currentEpisodeIndex = 0,
                        sourceName = video.sourceName,
                        sourceCode = video.sourceCode
                    )

                    PlayerScreen(
                        videoPlayInfo = playInfo,
                        onBackPressed = {
                            navController.popBackStack()
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun SimpleTestScreen() {
    var counter by remember { mutableStateOf(0) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "LibreTV Android 测试",
            style = MaterialTheme.typography.headlineMedium
        )

        Spacer(modifier = Modifier.height(32.dp))

        Text(
            text = "计数器: $counter",
            style = MaterialTheme.typography.titleLarge
        )

        Spacer(modifier = Modifier.height(16.dp))

        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Button(onClick = { counter++ }) {
                Text("增加")
            }

            Button(onClick = { counter-- }) {
                Text("减少")
            }

            Button(onClick = { counter = 0 }) {
                Text("重置")
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "✅ Compose UI 正常工作",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}