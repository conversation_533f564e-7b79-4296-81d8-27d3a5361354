package com.libretv.android

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.unit.dp
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.libretv.android.data.model.VideoItem
import com.libretv.android.data.model.VideoPlayInfo
import com.libretv.android.presentation.search.SearchScreen
import com.libretv.android.presentation.player.PlayerScreen
import com.libretv.android.ui.theme.LibreTVTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LibreTVTheme {
                LibreTVApp()
            }
        }
    }
}

@Composable
fun LibreTVApp() {
    val navController = rememberNavController()
    var selectedVideo by remember { mutableStateOf<VideoItem?>(null) }

    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = "search",
            modifier = Modifier.padding(innerPadding)
        ) {
            // 搜索页面
            composable("search") {
                SearchScreen(
                    onVideoClick = { video ->
                        selectedVideo = video
                        navController.navigate("player")
                    }
                )
            }

            // 播放器页面
            composable("player") {
                selectedVideo?.let { video ->
                    // 从视频详情中提取播放信息
                    val playInfo = VideoPlayInfo(
                        videoId = video.vodId,
                        title = video.vodName,
                        episodes = extractEpisodesFromVideo(video),
                        currentEpisodeIndex = 0,
                        sourceName = video.sourceName,
                        sourceCode = video.sourceCode
                    )

                    PlayerScreen(
                        videoPlayInfo = playInfo,
                        onBackPressed = {
                            navController.popBackStack()
                        }
                    )
                }
            }
        }
    }
}

/**
 * 从视频项中提取播放集数
 */
fun extractEpisodesFromVideo(video: VideoItem): List<String> {
    // 从vodContent中提取M3U8链接
    val content = video.vodContent ?: ""
    val m3u8Pattern = Regex("https?://[^\"'\\s]+?\\.m3u8")
    val matches = m3u8Pattern.findAll(content).map { it.value }.toList()

    return if (matches.isNotEmpty()) {
        matches
    } else {
        // 如果没有找到M3U8链接，返回测试链接
        listOf(
            "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8"
        )
    }
}

