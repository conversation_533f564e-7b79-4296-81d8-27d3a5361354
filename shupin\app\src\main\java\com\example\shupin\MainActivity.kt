package com.libretv.android

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.libretv.android.data.model.VideoItem
import com.libretv.android.presentation.search.SearchScreen
import com.libretv.android.ui.theme.LibreTVTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LibreTVTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    SearchScreen(
                        onVideoClick = { video ->
                            // TODO: 导航到播放器页面
                            handleVideoClick(video)
                        },
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }

    private fun handleVideoClick(video: VideoItem) {
        // TODO: 实现视频点击处理逻辑
        // 这里将来会导航到播放器页面
    }
}

@Preview(showBackground = true)
@Composable
fun LibreTVPreview() {
    LibreTVTheme {
        SearchScreen(
            onVideoClick = { }
        )
    }
}