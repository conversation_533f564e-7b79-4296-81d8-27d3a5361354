package com.libretv.android.presentation.player

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.libretv.android.data.model.PlayerState
import com.libretv.android.data.model.VideoPlayInfo
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel

/**
 * 播放器页面
 */
@Composable
fun PlayerScreen(
    videoPlayInfo: VideoPlayInfo,
    onBackPressed: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: PlayerViewModel = koinViewModel()
) {
    val playInfo by viewModel.playInfo.collectAsState()
    val playerState by viewModel.playerState.collectAsState()
    val isControlsVisible by viewModel.isControlsVisible.collectAsState()
    val currentPosition by viewModel.currentPosition.collectAsState()
    
    // 初始化播放信息
    LaunchedEffect(videoPlayInfo) {
        viewModel.initializePlayer(videoPlayInfo)
    }
    
    // 自动隐藏控制界面
    LaunchedEffect(isControlsVisible) {
        if (isControlsVisible && playerState is PlayerState.Playing) {
            delay(5000) // 5秒后自动隐藏
            viewModel.hideControls()
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // 视频播放器
        playInfo?.let { info ->
            ExoPlayerComposable(
                playInfo = info,
                onPlayerStateChanged = { state ->
                    viewModel.setPlayerState(state)
                },
                onPositionChanged = { position ->
                    viewModel.updatePlaybackPosition(position)
                },
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // 点击区域（显示/隐藏控制界面）
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable { 
                    viewModel.toggleControlsVisibility()
                }
        )
        
        // 播放控制界面
        if (isControlsVisible) {
            PlayerControls(
                playInfo = playInfo,
                playerState = playerState,
                currentPosition = currentPosition,
                onBackPressed = onBackPressed,
                onPreviousEpisode = viewModel::playPreviousEpisode,
                onNextEpisode = viewModel::playNextEpisode,
                onEpisodeSelected = viewModel::changeEpisode,
                hasPreviousEpisode = viewModel.hasPreviousEpisode(),
                hasNextEpisode = viewModel.hasNextEpisode()
            )
        }
        
        // 加载指示器
        if (playerState is PlayerState.Loading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
        
        // 错误提示
        when (val state = playerState) {
            is PlayerState.Error -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Card(
                        modifier = Modifier.padding(16.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.Black.copy(alpha = 0.8f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "播放错误",
                                style = MaterialTheme.typography.titleMedium,
                                color = Color.White
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = state.message,
                                style = MaterialTheme.typography.bodyMedium,
                                color = Color.White.copy(alpha = 0.7f),
                                textAlign = TextAlign.Center
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Button(
                                onClick = {
                                    // 重试播放
                                    playInfo?.let { info ->
                                        viewModel.initializePlayer(info)
                                    }
                                }
                            ) {
                                Text("重试")
                            }
                        }
                    }
                }
            }
            else -> {}
        }
    }
}

/**
 * 播放器控制界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PlayerControls(
    playInfo: VideoPlayInfo?,
    playerState: PlayerState,
    currentPosition: Long,
    onBackPressed: () -> Unit,
    onPreviousEpisode: () -> Unit,
    onNextEpisode: () -> Unit,
    onEpisodeSelected: (Int) -> Unit,
    hasPreviousEpisode: Boolean,
    hasNextEpisode: Boolean
) {
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.3f))
    ) {
        // 顶部控制栏
        TopAppBar(
            title = { 
                Text(
                    text = playInfo?.title ?: "",
                    color = Color.White,
                    maxLines = 1
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent
            )
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 中间控制区域
        if (playInfo != null && playInfo.episodes.size > 1) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 上一集按钮
                IconButton(
                    onClick = onPreviousEpisode,
                    enabled = hasPreviousEpisode
                ) {
                    Icon(
                        Icons.Default.KeyboardArrowLeft,
                        contentDescription = "上一集",
                        tint = if (hasPreviousEpisode) Color.White else Color.Gray,
                        modifier = Modifier.size(32.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(32.dp))
                
                // 集数信息
                Text(
                    text = "第${playInfo.currentEpisodeIndex + 1}集 / 共${playInfo.episodes.size}集",
                    color = Color.White,
                    style = MaterialTheme.typography.bodyLarge
                )
                
                Spacer(modifier = Modifier.width(32.dp))
                
                // 下一集按钮
                IconButton(
                    onClick = onNextEpisode,
                    enabled = hasNextEpisode
                ) {
                    Icon(
                        Icons.Default.KeyboardArrowRight,
                        contentDescription = "下一集",
                        tint = if (hasNextEpisode) Color.White else Color.Gray,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 底部集数选择器（如果有多集）
        if (playInfo != null && playInfo.episodes.size > 1) {
            LazyRow(
                modifier = Modifier.padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(playInfo.episodes.indices.toList()) { index ->
                    EpisodeButton(
                        episodeNumber = index + 1,
                        isSelected = index == playInfo.currentEpisodeIndex,
                        onClick = { onEpisodeSelected(index) }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * 集数按钮
 */
@Composable
private fun EpisodeButton(
    episodeNumber: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) 
                MaterialTheme.colorScheme.primary 
            else 
                Color.White.copy(alpha = 0.2f)
        ),
        modifier = Modifier.size(48.dp)
    ) {
        Text(
            text = episodeNumber.toString(),
            color = Color.White,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
