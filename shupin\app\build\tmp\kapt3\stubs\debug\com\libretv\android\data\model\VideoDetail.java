package com.libretv.android.data.model;

/**
 * 视频详情数据模型
 */
@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b7\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u0087\b\u0018\u0000 A2\u00020\u0001:\u0002@AB\u0097\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0010\u0010\u0011J\t\u0010,\u001a\u00020\u0003H\u00c6\u0003J\t\u0010-\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010.\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010/\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00100\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00101\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00102\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00103\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00104\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00105\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00106\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u00107\u001a\u00020\u0003H\u00c6\u0003J\t\u00108\u001a\u00020\u0003H\u00c6\u0003J\u009d\u0001\u00109\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010:\u001a\u00020;2\b\u0010<\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010=\u001a\u00020>H\u00d6\u0001J\t\u0010?\u001a\u00020\u0003H\u00d6\u0001R\u001c\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0012\u0010\u0013\u001a\u0004\b\u0014\u0010\u0015R\u001c\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0016\u0010\u0013\u001a\u0004\b\u0017\u0010\u0015R\u001e\u0010\u0005\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0018\u0010\u0013\u001a\u0004\b\u0019\u0010\u0015R\u001e\u0010\u0006\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001a\u0010\u0013\u001a\u0004\b\u001b\u0010\u0015R\u001e\u0010\u0007\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001c\u0010\u0013\u001a\u0004\b\u001d\u0010\u0015R\u001e\u0010\b\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001e\u0010\u0013\u001a\u0004\b\u001f\u0010\u0015R\u001e\u0010\t\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b \u0010\u0013\u001a\u0004\b!\u0010\u0015R\u001e\u0010\n\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\"\u0010\u0013\u001a\u0004\b#\u0010\u0015R\u001e\u0010\u000b\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b$\u0010\u0013\u001a\u0004\b%\u0010\u0015R\u001e\u0010\f\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b&\u0010\u0013\u001a\u0004\b\'\u0010\u0015R\u001e\u0010\r\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b(\u0010\u0013\u001a\u0004\b)\u0010\u0015R\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u0015R\u0011\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u0015\u00a8\u0006B"}, d2 = {"Lcom/libretv/android/data/model/VideoDetail;", "", "vodId", "", "vodName", "vodPic", "vodContent", "vodPlayUrl", "typeName", "vodYear", "vodArea", "vodDirector", "vodActor", "vodRemarks", "sourceName", "sourceCode", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getVodId$annotations", "()V", "getVodId", "()Ljava/lang/String;", "getVodName$annotations", "getVodName", "getVodPic$annotations", "getVodPic", "getVodContent$annotations", "getVodContent", "getVodPlayUrl$annotations", "getVodPlayUrl", "getTypeName$annotations", "getTypeName", "getVodYear$annotations", "getVodYear", "getVodArea$annotations", "getVodArea", "getVodDirector$annotations", "getVodDirector", "getVodActor$annotations", "getVodActor", "getVodRemarks$annotations", "getVodRemarks", "getSourceName", "getSourceCode", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "copy", "equals", "", "other", "hashCode", "", "toString", "$serializer", "Companion", "app_debug"})
public final class VideoDetail {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String vodId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String vodName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vodPic = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vodContent = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vodPlayUrl = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String typeName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vodYear = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vodArea = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vodDirector = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vodActor = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vodRemarks = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sourceName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sourceCode = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.libretv.android.data.model.VideoDetail.Companion Companion = null;
    
    public VideoDetail(@org.jetbrains.annotations.NotNull()
    java.lang.String vodId, @org.jetbrains.annotations.NotNull()
    java.lang.String vodName, @org.jetbrains.annotations.Nullable()
    java.lang.String vodPic, @org.jetbrains.annotations.Nullable()
    java.lang.String vodContent, @org.jetbrains.annotations.Nullable()
    java.lang.String vodPlayUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String typeName, @org.jetbrains.annotations.Nullable()
    java.lang.String vodYear, @org.jetbrains.annotations.Nullable()
    java.lang.String vodArea, @org.jetbrains.annotations.Nullable()
    java.lang.String vodDirector, @org.jetbrains.annotations.Nullable()
    java.lang.String vodActor, @org.jetbrains.annotations.Nullable()
    java.lang.String vodRemarks, @org.jetbrains.annotations.NotNull()
    java.lang.String sourceName, @org.jetbrains.annotations.NotNull()
    java.lang.String sourceCode) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVodId() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_id")
    @java.lang.Deprecated()
    public static void getVodId$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVodName() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_name")
    @java.lang.Deprecated()
    public static void getVodName$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVodPic() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_pic")
    @java.lang.Deprecated()
    public static void getVodPic$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVodContent() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_content")
    @java.lang.Deprecated()
    public static void getVodContent$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVodPlayUrl() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_play_url")
    @java.lang.Deprecated()
    public static void getVodPlayUrl$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getTypeName() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "type_name")
    @java.lang.Deprecated()
    public static void getTypeName$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVodYear() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_year")
    @java.lang.Deprecated()
    public static void getVodYear$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVodArea() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_area")
    @java.lang.Deprecated()
    public static void getVodArea$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVodDirector() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_director")
    @java.lang.Deprecated()
    public static void getVodDirector$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVodActor() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_actor")
    @java.lang.Deprecated()
    public static void getVodActor$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVodRemarks() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "vod_remarks")
    @java.lang.Deprecated()
    public static void getVodRemarks$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSourceName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSourceCode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.libretv.android.data.model.VideoDetail copy(@org.jetbrains.annotations.NotNull()
    java.lang.String vodId, @org.jetbrains.annotations.NotNull()
    java.lang.String vodName, @org.jetbrains.annotations.Nullable()
    java.lang.String vodPic, @org.jetbrains.annotations.Nullable()
    java.lang.String vodContent, @org.jetbrains.annotations.Nullable()
    java.lang.String vodPlayUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String typeName, @org.jetbrains.annotations.Nullable()
    java.lang.String vodYear, @org.jetbrains.annotations.Nullable()
    java.lang.String vodArea, @org.jetbrains.annotations.Nullable()
    java.lang.String vodDirector, @org.jetbrains.annotations.Nullable()
    java.lang.String vodActor, @org.jetbrains.annotations.Nullable()
    java.lang.String vodRemarks, @org.jetbrains.annotations.NotNull()
    java.lang.String sourceName, @org.jetbrains.annotations.NotNull()
    java.lang.String sourceCode) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * 视频详情数据模型
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/libretv/android/data/model/VideoDetail.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/libretv/android/data/model/VideoDetail;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_debug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.libretv.android.data.model.VideoDetail> {
        @org.jetbrains.annotations.NotNull()
        public static final com.libretv.android.data.model.VideoDetail.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        /**
         * 视频详情数据模型
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        /**
         * 视频详情数据模型
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.libretv.android.data.model.VideoDetail deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        /**
         * 视频详情数据模型
         */
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.libretv.android.data.model.VideoDetail value) {
        }
        
        private $serializer() {
            super();
        }
        
        /**
         * 视频详情数据模型
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public kotlinx.serialization.KSerializer<?>[] typeParametersSerializers() {
            return null;
        }
    }
    
    /**
     * 视频详情数据模型
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/libretv/android/data/model/VideoDetail$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/libretv/android/data/model/VideoDetail;", "app_debug"})
    public static final class Companion {
        
        /**
         * 视频详情数据模型
         */
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.libretv.android.data.model.VideoDetail> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}