package com.libretv.android.presentation.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.libretv.android.data.model.SearchState
import com.libretv.android.data.model.VideoItem
import com.libretv.android.data.repository.VideoRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 搜索页面ViewModel
 */
class SearchViewModel(
    private val videoRepository: VideoRepository
) : ViewModel() {
    
    private val _searchState = MutableStateFlow<SearchState>(SearchState.Idle)
    val searchState: StateFlow<SearchState> = _searchState.asStateFlow()
    
    private val _searchHistory = MutableStateFlow<List<String>>(emptyList())
    val searchHistory: StateFlow<List<String>> = _searchHistory.asStateFlow()
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    init {
        loadSearchHistory()
    }
    
    /**
     * 搜索视频
     */
    fun searchVideos(keyword: String) {
        if (keyword.isBlank()) {
            _searchState.value = SearchState.Idle
            return
        }
        
        viewModelScope.launch {
            videoRepository.searchVideos(keyword).collect { result ->
                _searchState.value = result
            }
            
            // 保存搜索历史
            videoRepository.saveSearchHistory(keyword)
            loadSearchHistory()
        }
    }
    
    /**
     * 更新搜索查询
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * 清除搜索结果
     */
    fun clearSearchResults() {
        _searchState.value = SearchState.Idle
    }
    
    /**
     * 重试搜索
     */
    fun retrySearch() {
        val currentQuery = _searchQuery.value
        if (currentQuery.isNotBlank()) {
            searchVideos(currentQuery)
        }
    }
    
    /**
     * 加载搜索历史
     */
    private fun loadSearchHistory() {
        viewModelScope.launch {
            videoRepository.getSearchHistory().collect { history ->
                _searchHistory.value = history
            }
        }
    }
    
    /**
     * 删除搜索历史项
     */
    fun removeSearchHistoryItem(keyword: String) {
        viewModelScope.launch {
            // 这里需要在VideoRepository中添加删除单个历史记录的方法
            loadSearchHistory()
        }
    }
    
    /**
     * 清除所有搜索历史
     */
    fun clearSearchHistory() {
        viewModelScope.launch {
            // 这里需要在VideoRepository中添加清除所有历史记录的方法
            loadSearchHistory()
        }
    }
    
    /**
     * 从历史记录搜索
     */
    fun searchFromHistory(keyword: String) {
        updateSearchQuery(keyword)
        searchVideos(keyword)
    }
    
    /**
     * 获取当前搜索结果数量
     */
    fun getSearchResultCount(): Int {
        return when (val state = _searchState.value) {
            is SearchState.Success -> state.videos.size
            else -> 0
        }
    }
    
    /**
     * 检查是否有搜索结果
     */
    fun hasSearchResults(): Boolean {
        return _searchState.value is SearchState.Success
    }
    
    /**
     * 检查是否正在搜索
     */
    fun isSearching(): Boolean {
        return _searchState.value is SearchState.Loading
    }
}
