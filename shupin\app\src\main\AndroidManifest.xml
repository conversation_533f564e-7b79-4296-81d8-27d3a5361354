<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- 唤醒锁权限（用于视频播放） -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:name="com.libretv.android.LibreTVApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Shupin"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <activity
            android:name="com.libretv.android.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Shupin">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.libretv.android.TestActivity"
            android:exported="false"
            android:label="LibreTV Test"
            android:theme="@style/Theme.Shupin" />

    </application>

</manifest>