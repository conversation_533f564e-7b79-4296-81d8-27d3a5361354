package com.libretv.android.presentation.player;

/**
 * 播放器ViewModel
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u000e\n\u0002\u0010\u000e\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u000e\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u000eJ\u000e\u0010\u001b\u001a\u00020\u00192\u0006\u0010\u001c\u001a\u00020\u001dJ\u0006\u0010\u001e\u001a\u00020\u0019J\u0006\u0010\u001f\u001a\u00020\u0019J\u000e\u0010 \u001a\u00020\u00192\u0006\u0010!\u001a\u00020\u0015J\u0006\u0010\"\u001a\u00020\u0019J\u0006\u0010#\u001a\u00020\u0019J\u0006\u0010$\u001a\u00020\u0019J\u000e\u0010%\u001a\u00020\u00192\u0006\u0010&\u001a\u00020\bJ\u0006\u0010\'\u001a\u00020\u0019J\u0006\u0010(\u001a\u00020\u0019J\u0006\u0010)\u001a\u00020\u0019J\u000e\u0010*\u001a\u00020\u00192\u0006\u0010+\u001a\u00020,J\b\u0010-\u001a\u0004\u0018\u00010,J\u0006\u0010.\u001a\u00020\u0012J\u0006\u0010/\u001a\u00020\u0012J\u0006\u00100\u001a\u00020,R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0016\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\fR\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00120\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\fR\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00150\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\f\u00a8\u00061"}, d2 = {"Lcom/libretv/android/presentation/player/PlayerViewModel;", "Landroidx/lifecycle/ViewModel;", "videoRepository", "Lcom/libretv/android/data/repository/VideoRepository;", "<init>", "(Lcom/libretv/android/data/repository/VideoRepository;)V", "_playerState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/libretv/android/data/model/PlayerState;", "playerState", "Lkotlinx/coroutines/flow/StateFlow;", "getPlayerState", "()Lkotlinx/coroutines/flow/StateFlow;", "_playInfo", "Lcom/libretv/android/data/model/VideoPlayInfo;", "playInfo", "getPlayInfo", "_isControlsVisible", "", "isControlsVisible", "_currentPosition", "", "currentPosition", "getCurrentPosition", "initializePlayer", "", "videoPlayInfo", "changeEpisode", "episodeIndex", "", "playPreviousEpisode", "playNextEpisode", "updatePlaybackPosition", "position", "toggleControlsVisibility", "showControls", "hideControls", "setPlayerState", "state", "play", "pause", "stop", "onError", "message", "", "getCurrentVideoUrl", "hasPreviousEpisode", "hasNextEpisode", "getCurrentEpisodeInfo", "app_debug"})
public final class PlayerViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.libretv.android.data.repository.VideoRepository videoRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.libretv.android.data.model.PlayerState> _playerState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.libretv.android.data.model.PlayerState> playerState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.libretv.android.data.model.VideoPlayInfo> _playInfo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.libretv.android.data.model.VideoPlayInfo> playInfo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isControlsVisible = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isControlsVisible = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Long> _currentPosition = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Long> currentPosition = null;
    
    public PlayerViewModel(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.repository.VideoRepository videoRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.libretv.android.data.model.PlayerState> getPlayerState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.libretv.android.data.model.VideoPlayInfo> getPlayInfo() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isControlsVisible() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Long> getCurrentPosition() {
        return null;
    }
    
    /**
     * 初始化播放信息
     */
    public final void initializePlayer(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.model.VideoPlayInfo videoPlayInfo) {
    }
    
    /**
     * 切换集数
     */
    public final void changeEpisode(int episodeIndex) {
    }
    
    /**
     * 播放上一集
     */
    public final void playPreviousEpisode() {
    }
    
    /**
     * 播放下一集
     */
    public final void playNextEpisode() {
    }
    
    /**
     * 更新播放位置
     */
    public final void updatePlaybackPosition(long position) {
    }
    
    /**
     * 切换控制界面可见性
     */
    public final void toggleControlsVisibility() {
    }
    
    /**
     * 显示控制界面
     */
    public final void showControls() {
    }
    
    /**
     * 隐藏控制界面
     */
    public final void hideControls() {
    }
    
    /**
     * 设置播放状态
     */
    public final void setPlayerState(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.model.PlayerState state) {
    }
    
    /**
     * 播放
     */
    public final void play() {
    }
    
    /**
     * 暂停
     */
    public final void pause() {
    }
    
    /**
     * 停止
     */
    public final void stop() {
    }
    
    /**
     * 播放错误
     */
    public final void onError(@org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    /**
     * 获取当前播放的视频URL
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCurrentVideoUrl() {
        return null;
    }
    
    /**
     * 检查是否有上一集
     */
    public final boolean hasPreviousEpisode() {
        return false;
    }
    
    /**
     * 检查是否有下一集
     */
    public final boolean hasNextEpisode() {
        return false;
    }
    
    /**
     * 获取当前集数信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCurrentEpisodeInfo() {
        return null;
    }
}