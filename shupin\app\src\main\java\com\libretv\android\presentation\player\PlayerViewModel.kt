package com.libretv.android.presentation.player

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.libretv.android.data.model.PlayerState
import com.libretv.android.data.model.VideoPlayInfo
import com.libretv.android.data.repository.VideoRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 播放器ViewModel
 */
class PlayerViewModel(
    private val videoRepository: VideoRepository
) : ViewModel() {
    
    private val _playerState = MutableStateFlow<PlayerState>(PlayerState.Loading)
    val playerState: StateFlow<PlayerState> = _playerState.asStateFlow()
    
    private val _playInfo = MutableStateFlow<VideoPlayInfo?>(null)
    val playInfo: StateFlow<VideoPlayInfo?> = _playInfo.asStateFlow()
    
    private val _isControlsVisible = MutableStateFlow(true)
    val isControlsVisible: StateFlow<Boolean> = _isControlsVisible.asStateFlow()
    
    private val _currentPosition = MutableStateFlow(0L)
    val currentPosition: StateFlow<Long> = _currentPosition.asStateFlow()
    
    /**
     * 初始化播放信息
     */
    fun initializePlayer(videoPlayInfo: VideoPlayInfo) {
        _playInfo.value = videoPlayInfo
        _playerState.value = PlayerState.Ready
    }
    
    /**
     * 切换集数
     */
    fun changeEpisode(episodeIndex: Int) {
        val currentPlayInfo = _playInfo.value ?: return
        
        if (episodeIndex >= 0 && episodeIndex < currentPlayInfo.episodes.size) {
            val newPlayInfo = currentPlayInfo.copy(
                currentEpisodeIndex = episodeIndex,
                lastPlayPosition = 0L // 重置播放位置
            )
            _playInfo.value = newPlayInfo
            _playerState.value = PlayerState.Loading
        }
    }
    
    /**
     * 播放上一集
     */
    fun playPreviousEpisode() {
        val currentPlayInfo = _playInfo.value ?: return
        if (currentPlayInfo.currentEpisodeIndex > 0) {
            changeEpisode(currentPlayInfo.currentEpisodeIndex - 1)
        }
    }
    
    /**
     * 播放下一集
     */
    fun playNextEpisode() {
        val currentPlayInfo = _playInfo.value ?: return
        if (currentPlayInfo.currentEpisodeIndex < currentPlayInfo.episodes.size - 1) {
            changeEpisode(currentPlayInfo.currentEpisodeIndex + 1)
        }
    }
    
    /**
     * 更新播放位置
     */
    fun updatePlaybackPosition(position: Long) {
        _currentPosition.value = position
        
        // 更新播放信息中的位置
        val currentPlayInfo = _playInfo.value
        if (currentPlayInfo != null) {
            _playInfo.value = currentPlayInfo.copy(lastPlayPosition = position)
        }
    }
    
    /**
     * 切换控制界面可见性
     */
    fun toggleControlsVisibility() {
        _isControlsVisible.value = !_isControlsVisible.value
    }
    
    /**
     * 显示控制界面
     */
    fun showControls() {
        _isControlsVisible.value = true
    }
    
    /**
     * 隐藏控制界面
     */
    fun hideControls() {
        _isControlsVisible.value = false
    }
    
    /**
     * 设置播放状态
     */
    fun setPlayerState(state: PlayerState) {
        _playerState.value = state
    }
    
    /**
     * 播放
     */
    fun play() {
        _playerState.value = PlayerState.Playing
    }
    
    /**
     * 暂停
     */
    fun pause() {
        _playerState.value = PlayerState.Paused
    }
    
    /**
     * 停止
     */
    fun stop() {
        _playerState.value = PlayerState.Ready
        _currentPosition.value = 0L
    }
    
    /**
     * 播放错误
     */
    fun onError(message: String) {
        _playerState.value = PlayerState.Error(message)
    }
    
    /**
     * 获取当前播放的视频URL
     */
    fun getCurrentVideoUrl(): String? {
        val currentPlayInfo = _playInfo.value ?: return null
        return if (currentPlayInfo.episodes.isNotEmpty() && 
                   currentPlayInfo.currentEpisodeIndex < currentPlayInfo.episodes.size) {
            currentPlayInfo.episodes[currentPlayInfo.currentEpisodeIndex]
        } else {
            null
        }
    }
    
    /**
     * 检查是否有上一集
     */
    fun hasPreviousEpisode(): Boolean {
        val currentPlayInfo = _playInfo.value ?: return false
        return currentPlayInfo.currentEpisodeIndex > 0
    }
    
    /**
     * 检查是否有下一集
     */
    fun hasNextEpisode(): Boolean {
        val currentPlayInfo = _playInfo.value ?: return false
        return currentPlayInfo.currentEpisodeIndex < currentPlayInfo.episodes.size - 1
    }
    
    /**
     * 获取当前集数信息
     */
    fun getCurrentEpisodeInfo(): String {
        val currentPlayInfo = _playInfo.value ?: return ""
        return "第${currentPlayInfo.currentEpisodeIndex + 1}集 / 共${currentPlayInfo.episodes.size}集"
    }
}
