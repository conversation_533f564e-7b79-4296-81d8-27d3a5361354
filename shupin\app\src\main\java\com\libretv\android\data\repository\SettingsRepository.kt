package com.libretv.android.data.repository

import android.content.SharedPreferences
import com.libretv.android.data.api.ApiConfig
import com.libretv.android.data.api.ApiSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * 设置数据仓库
 */
class SettingsRepository(
    private val sharedPreferences: SharedPreferences
) {
    
    companion object {
        private const val KEY_SELECTED_APIS = "selected_apis"
        private const val KEY_YELLOW_FILTER_ENABLED = "yellow_filter_enabled"
        private const val KEY_AD_FILTERING_ENABLED = "ad_filtering_enabled"
        private const val KEY_SEARCH_HISTORY = "search_history"
        private const val KEY_AUTOPLAY_ENABLED = "autoplay_enabled"
        private const val MAX_SEARCH_HISTORY = 5
    }
    
    /**
     * 获取选中的API源
     */
    fun getSelectedApis(): List<ApiSource> {
        val selectedCodes = sharedPreferences.getStringSet(KEY_SELECTED_APIS, null)
            ?: ApiConfig.DEFAULT_SELECTED_APIS
        
        return ApiConfig.API_SOURCES.filter { api ->
            selectedCodes.contains(api.code)
        }
    }
    
    /**
     * 设置选中的API源
     */
    fun setSelectedApis(apiCodes: Set<String>) {
        sharedPreferences.edit()
            .putStringSet(KEY_SELECTED_APIS, apiCodes)
            .apply()
    }
    
    /**
     * 是否启用黄色内容过滤
     */
    fun isYellowFilterEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_YELLOW_FILTER_ENABLED, true)
    }
    
    /**
     * 设置黄色内容过滤
     */
    fun setYellowFilterEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_YELLOW_FILTER_ENABLED, enabled)
            .apply()
    }
    
    /**
     * 是否启用广告过滤
     */
    fun isAdFilteringEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_AD_FILTERING_ENABLED, true)
    }
    
    /**
     * 设置广告过滤
     */
    fun setAdFilteringEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_AD_FILTERING_ENABLED, enabled)
            .apply()
    }
    
    /**
     * 是否启用自动播放
     */
    fun isAutoplayEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTOPLAY_ENABLED, true)
    }
    
    /**
     * 设置自动播放
     */
    fun setAutoplayEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_AUTOPLAY_ENABLED, enabled)
            .apply()
    }
    
    /**
     * 获取搜索历史
     */
    fun getSearchHistory(): Flow<List<String>> = flow {
        val historyString = sharedPreferences.getString(KEY_SEARCH_HISTORY, "") ?: ""
        val history = if (historyString.isNotEmpty()) {
            historyString.split(",").filter { it.isNotBlank() }
        } else {
            emptyList()
        }
        emit(history)
    }
    
    /**
     * 保存搜索历史
     */
    fun saveSearchHistory(keyword: String) {
        val currentHistory = getSearchHistorySync().toMutableList()
        
        // 移除已存在的相同关键词
        currentHistory.remove(keyword)
        
        // 添加到最前面
        currentHistory.add(0, keyword)
        
        // 限制数量
        if (currentHistory.size > MAX_SEARCH_HISTORY) {
            currentHistory.removeAt(currentHistory.size - 1)
        }
        
        // 保存
        val historyString = currentHistory.joinToString(",")
        sharedPreferences.edit()
            .putString(KEY_SEARCH_HISTORY, historyString)
            .apply()
    }
    
    /**
     * 清除搜索历史
     */
    fun clearSearchHistory() {
        sharedPreferences.edit()
            .remove(KEY_SEARCH_HISTORY)
            .apply()
    }
    
    /**
     * 删除单个搜索历史
     */
    fun removeSearchHistory(keyword: String) {
        val currentHistory = getSearchHistorySync().toMutableList()
        currentHistory.remove(keyword)
        
        val historyString = currentHistory.joinToString(",")
        sharedPreferences.edit()
            .putString(KEY_SEARCH_HISTORY, historyString)
            .apply()
    }
    
    /**
     * 同步获取搜索历史
     */
    private fun getSearchHistorySync(): List<String> {
        val historyString = sharedPreferences.getString(KEY_SEARCH_HISTORY, "") ?: ""
        return if (historyString.isNotEmpty()) {
            historyString.split(",").filter { it.isNotBlank() }
        } else {
            emptyList()
        }
    }
    
    /**
     * 获取所有设置的摘要
     */
    fun getSettingsSummary(): Map<String, Any> {
        return mapOf(
            "selectedApis" to getSelectedApis().map { it.code },
            "yellowFilterEnabled" to isYellowFilterEnabled(),
            "adFilteringEnabled" to isAdFilteringEnabled(),
            "autoplayEnabled" to isAutoplayEnabled(),
            "searchHistoryCount" to getSearchHistorySync().size
        )
    }
    
    /**
     * 重置所有设置为默认值
     */
    fun resetToDefaults() {
        sharedPreferences.edit()
            .putStringSet(KEY_SELECTED_APIS, ApiConfig.DEFAULT_SELECTED_APIS)
            .putBoolean(KEY_YELLOW_FILTER_ENABLED, true)
            .putBoolean(KEY_AD_FILTERING_ENABLED, true)
            .putBoolean(KEY_AUTOPLAY_ENABLED, true)
            .remove(KEY_SEARCH_HISTORY)
            .apply()
    }
}
