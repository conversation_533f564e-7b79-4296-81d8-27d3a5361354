package com.libretv.android;

/**
 * LibreTV应用程序类
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\b\u0010\u0004\u001a\u00020\u0005H\u0016\u00a8\u0006\u0006"}, d2 = {"Lcom/libretv/android/LibreTVApplication;", "Landroid/app/Application;", "<init>", "()V", "onCreate", "", "app_debug"})
public final class LibreTVApplication extends android.app.Application {
    
    public LibreTVApplication() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
}