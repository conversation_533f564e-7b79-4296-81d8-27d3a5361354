package com.libretv.android.presentation.player;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\u001aB\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u001a8\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0002\u001a\"\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\f2\u0006\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\bH\u0002\u00a8\u0006\u0014"}, d2 = {"ExoPlayerComposable", "", "playInfo", "Lcom/libretv/android/data/model/VideoPlayInfo;", "onPlayerStateChanged", "Lkotlin/Function1;", "Lcom/libretv/android/data/model/PlayerState;", "onPositionChanged", "", "modifier", "Landroidx/compose/ui/Modifier;", "createExoPlayer", "Lcom/google/android/exoplayer2/ExoPlayer;", "context", "Landroid/content/Context;", "loadVideo", "exoPlayer", "videoUrl", "", "startPosition", "app_debug"})
public final class ExoPlayerComposableKt {
    
    /**
     * ExoPlayer Compose组件
     * 使用官方ExoPlayer实现视频播放
     */
    @androidx.compose.runtime.Composable()
    public static final void ExoPlayerComposable(@org.jetbrains.annotations.NotNull()
    com.libretv.android.data.model.VideoPlayInfo playInfo, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.libretv.android.data.model.PlayerState, kotlin.Unit> onPlayerStateChanged, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Long, kotlin.Unit> onPositionChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 创建ExoPlayer实例
     */
    private static final com.google.android.exoplayer2.ExoPlayer createExoPlayer(android.content.Context context, kotlin.jvm.functions.Function1<? super com.libretv.android.data.model.PlayerState, kotlin.Unit> onPlayerStateChanged, kotlin.jvm.functions.Function1<? super java.lang.Long, kotlin.Unit> onPositionChanged) {
        return null;
    }
    
    /**
     * 加载视频
     */
    private static final void loadVideo(com.google.android.exoplayer2.ExoPlayer exoPlayer, java.lang.String videoUrl, long startPosition) {
    }
}