package com.libretv.android.data.api

/**
 * API源配置
 */
data class ApiSource(
    val code: String,
    val name: String,
    val baseUrl: String,
    val detailUrl: String? = null,
    val isAdult: Boolean = false
)

/**
 * API配置常量
 */
object ApiConfig {
    
    // 代理URL前缀
    const val PROXY_URL = "http://localhost:8080/proxy/"
    
    // 搜索路径
    const val SEARCH_PATH = "?ac=videolist&wd="
    
    // 详情路径
    const val DETAIL_PATH = "?ac=videolist&ids="
    
    // 请求超时时间
    const val TIMEOUT_SECONDS = 10L
    
    // 搜索和详情路径配置
    const val SEARCH_PATH = "?ac=videolist&wd="
    const val DETAIL_PATH = "?ac=videolist&ids="

    // 内置API源列表（基于LibreTV原项目）
    val API_SOURCES = listOf(
        ApiSource(
            code = "dyttzy",
            name = "电影天堂资源",
            baseUrl = "http://caiji.dyttzyapi.com/api.php/provide/vod",
            searchPath = SEARCH_PATH,
            detailPath = DETAIL_PATH,
            detailUrl = "http://caiji.dyttzyapi.com"
        ),
        ApiSource(
            code = "ruyi",
            name = "如意资源",
            baseUrl = "https://cj.rycjapi.com/api.php/provide/vod",
            searchPath = SEARCH_PATH,
            detailPath = DETAIL_PATH
        ),
        ApiSource(
            code = "bfzy",
            name = "暴风资源",
            baseUrl = "https://bfzyapi.com/api.php/provide/vod",
            searchPath = SEARCH_PATH,
            detailPath = DETAIL_PATH
        ),
        ApiSource(
            code = "tyyszy",
            name = "天涯资源",
            baseUrl = "https://tyyszy.com/api.php/provide/vod",
            searchPath = SEARCH_PATH,
            detailPath = DETAIL_PATH
        ),
        ApiSource(
            code = "ffzy",
            name = "非凡影视",
            baseUrl = "http://ffzy5.tv/api.php/provide/vod",
            searchPath = SEARCH_PATH,
            detailPath = DETAIL_PATH,
            detailUrl = "http://ffzy5.tv"
        ),
        ApiSource(
            code = "heimuer",
            name = "黑木耳",
            baseUrl = "https://json.heimuer.xyz/api.php/provide/vod",
            searchPath = SEARCH_PATH,
            detailPath = DETAIL_PATH,
            detailUrl = "https://heimuer.tv"
        ),
        ApiSource(
            code = "zy360",
            name = "360资源",
            baseUrl = "https://360zy.com/api.php/provide/vod",
            searchPath = SEARCH_PATH,
            detailPath = DETAIL_PATH
        ),
        ApiSource(
            code = "wolong",
            name = "卧龙资源",
            baseUrl = "https://wolongzyw.com/api.php/provide/vod",
            searchPath = SEARCH_PATH,
            detailPath = DETAIL_PATH
        )
    )
    
    // 默认选中的API源（基于原项目配置）
    val DEFAULT_SELECTED_APIS = setOf(
        "tyyszy", "dyttzy", "bfzy", "ruyi"
    )
    
    // 广告关键词列表（基于原项目的黄色内容过滤）
    val AD_KEYWORDS = listOf(
        "伦理片", "福利", "里番动漫", "门事件", "萝莉少女",
        "制服诱惑", "国产传媒", "cosplay", "黑丝诱惑",
        "无码", "日本无码", "有码", "日本有码", "SWAG",
        "网红主播", "色情片", "同性片", "福利视频", "福利片"
    )
    
    // M3U8正则表达式
    val M3U8_PATTERN = Regex("https?://[^\"'\\s]+?\\.m3u8")
    
    // 请求头
    val DEFAULT_HEADERS = mapOf(
        "User-Agent" to "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
        "Accept" to "application/json, text/plain, */*",
        "Accept-Language" to "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control" to "no-cache"
    )
}

/**
 * 聚合搜索配置
 */
object AggregatedSearchConfig {
    const val ENABLED = true
    const val TIMEOUT_MS = 8000L
    const val MAX_RESULTS = 10000
    const val PARALLEL_REQUESTS = true
    const val SHOW_SOURCE_BADGES = true
}

/**
 * 自定义API配置
 */
object CustomApiConfig {
    const val SEPARATOR = ","
    const val MAX_SOURCES = 5
    const val TEST_TIMEOUT_MS = 5000L
    const val NAME_PREFIX = "Custom-"
    const val VALIDATE_URL = true
}
