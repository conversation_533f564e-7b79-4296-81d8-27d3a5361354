package com.libretv.android.data.api

/**
 * API源配置
 */
data class ApiSource(
    val code: String,
    val name: String,
    val baseUrl: String,
    val detailUrl: String? = null,
    val isAdult: Boolean = false
)

/**
 * API配置常量
 */
object ApiConfig {
    
    // 代理URL前缀
    const val PROXY_URL = "http://localhost:8080/proxy/"
    
    // 搜索路径
    const val SEARCH_PATH = "?ac=videolist&wd="
    
    // 详情路径
    const val DETAIL_PATH = "?ac=videolist&ids="
    
    // 请求超时时间
    const val TIMEOUT_SECONDS = 10L
    
    // 内置API源列表（基于LibreTV原项目）
    val API_SOURCES = listOf(
        ApiSource(
            code = "ruyi",
            name = "如意资源",
            baseUrl = "https://cj.rycjapi.com/api.php/provide/vod"
        ),
        ApiSource(
            code = "bfzy",
            name = "暴风资源",
            baseUrl = "https://bfzyapi.com/api.php/provide/vod"
        ),
        ApiSource(
            code = "ffzy",
            name = "非凡影视",
            baseUrl = "http://ffzy5.tv/api.php/provide/vod",
            detailUrl = "http://ffzy5.tv"
        ),
        ApiSource(
            code = "heimuer",
            name = "黑木耳",
            baseUrl = "https://json.heimuer.xyz/api.php/provide/vod",
            detailUrl = "https://heimuer.tv"
        ),
        ApiSource(
            code = "zy360",
            name = "360资源",
            baseUrl = "https://360zy.com/api.php/provide/vod"
        ),
        ApiSource(
            code = "jisu",
            name = "极速资源",
            baseUrl = "https://jszyapi.com/api.php/provide/vod",
            detailUrl = "https://jszyapi.com"
        ),
        ApiSource(
            code = "dbzy",
            name = "豆瓣资源",
            baseUrl = "https://dbzy.com/api.php/provide/vod"
        ),
        ApiSource(
            code = "tyyszy",
            name = "天涯资源",
            baseUrl = "https://tyyszy.com/api.php/provide/vod"
        ),
        ApiSource(
            code = "kuaikan",
            name = "快看资源",
            baseUrl = "https://kuaikan-api.com/api.php/provide/vod"
        ),
        ApiSource(
            code = "leshi",
            name = "乐视资源",
            baseUrl = "https://leshiapi.com/api.php/provide/vod"
        )
    )
    
    // 默认选中的API源
    val DEFAULT_SELECTED_APIS = setOf(
        "ruyi", "bfzy", "ffzy", "heimuer", "zy360"
    )
    
    // 广告关键词列表
    val AD_KEYWORDS = listOf(
        "ad", "ads", "advert", "advertisement",
        "commercial", "sponsor", "promo", "广告"
    )
    
    // M3U8正则表达式
    val M3U8_PATTERN = Regex("https?://[^\"'\\s]+?\\.m3u8")
    
    // 请求头
    val DEFAULT_HEADERS = mapOf(
        "User-Agent" to "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
        "Accept" to "application/json, text/plain, */*",
        "Accept-Language" to "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control" to "no-cache"
    )
}

/**
 * 聚合搜索配置
 */
object AggregatedSearchConfig {
    const val ENABLED = true
    const val TIMEOUT_MS = 8000L
    const val MAX_RESULTS = 10000
    const val PARALLEL_REQUESTS = true
    const val SHOW_SOURCE_BADGES = true
}

/**
 * 自定义API配置
 */
object CustomApiConfig {
    const val SEPARATOR = ","
    const val MAX_SOURCES = 5
    const val TEST_TIMEOUT_MS = 5000L
    const val NAME_PREFIX = "Custom-"
    const val VALIDATE_URL = true
}
