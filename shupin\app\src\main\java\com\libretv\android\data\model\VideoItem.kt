package com.libretv.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 视频项数据模型
 */
@Serializable
data class VideoItem(
    @SerialName("vod_id")
    val vodId: String,
    
    @SerialName("vod_name")
    val vodName: String,
    
    @SerialName("vod_pic")
    val vodPic: String? = null,
    
    @SerialName("vod_remarks")
    val vodRemarks: String? = null,
    
    @SerialName("type_name")
    val typeName: String? = null,
    
    @SerialName("vod_year")
    val vodYear: String? = null,
    
    @SerialName("vod_area")
    val vodArea: String? = null,
    
    @SerialName("vod_director")
    val vodDirector: String? = null,
    
    @SerialName("vod_actor")
    val vodActor: String? = null,
    
    @SerialName("vod_content")
    val vodContent: String? = null,
    
    // 来源信息
    val sourceName: String = "",
    val sourceCode: String = ""
)

/**
 * API响应数据模型
 */
@Serializable
data class ApiResponse(
    val code: Int,
    val list: List<VideoItem> = emptyList(),
    val msg: String? = null,
    val pagecount: Int? = null
)

/**
 * 视频详情数据模型
 */
@Serializable
data class VideoDetail(
    @SerialName("vod_id")
    val vodId: String,
    
    @SerialName("vod_name")
    val vodName: String,
    
    @SerialName("vod_pic")
    val vodPic: String? = null,
    
    @SerialName("vod_content")
    val vodContent: String? = null,
    
    @SerialName("vod_play_url")
    val vodPlayUrl: String? = null,
    
    @SerialName("type_name")
    val typeName: String? = null,
    
    @SerialName("vod_year")
    val vodYear: String? = null,
    
    @SerialName("vod_area")
    val vodArea: String? = null,
    
    @SerialName("vod_director")
    val vodDirector: String? = null,
    
    @SerialName("vod_actor")
    val vodActor: String? = null,
    
    @SerialName("vod_remarks")
    val vodRemarks: String? = null,
    
    // 来源信息
    val sourceName: String = "",
    val sourceCode: String = ""
)

/**
 * 视频播放信息
 */
data class VideoPlayInfo(
    val videoId: String,
    val title: String,
    val episodes: List<String>,
    val currentEpisodeIndex: Int = 0,
    val lastPlayPosition: Long = 0L,
    val sourceName: String = "",
    val sourceCode: String = ""
)

/**
 * 搜索状态
 */
sealed class SearchState {
    object Idle : SearchState()
    object Loading : SearchState()
    data class Success(val videos: List<VideoItem>) : SearchState()
    data class Error(val message: String) : SearchState()
}

/**
 * 播放器状态
 */
sealed class PlayerState {
    object Loading : PlayerState()
    object Ready : PlayerState()
    object Playing : PlayerState()
    object Paused : PlayerState()
    data class Error(val message: String) : PlayerState()
}
