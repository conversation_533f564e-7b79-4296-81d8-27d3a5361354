/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity android.app.Application3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer+ *com.libretv.android.data.model.SearchState+ *com.libretv.android.data.model.SearchState+ *com.libretv.android.data.model.SearchState+ *com.libretv.android.data.model.SearchState+ *com.libretv.android.data.model.PlayerState+ *com.libretv.android.data.model.PlayerState+ *com.libretv.android.data.model.PlayerState+ *com.libretv.android.data.model.PlayerState+ *com.libretv.android.data.model.PlayerState androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel okhttp3.Interceptor$ #androidx.activity.ComponentActivity