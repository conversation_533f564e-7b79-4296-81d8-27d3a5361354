package com.libretv.android.utils;

/**
 * 本地代理服务器
 * 用于处理跨域请求和广告过滤
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0003\u0018\u0000 \u00152\u00020\u0001:\u0001\u0015B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0016\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u001a\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\t2\b\u0010\u000f\u001a\u0004\u0018\u00010\tH\u0002J\u000e\u0010\u0010\u001a\u00020\t2\u0006\u0010\u0011\u001a\u00020\tJ\u0006\u0010\u0012\u001a\u00020\u0013J\u0006\u0010\u0014\u001a\u00020\u0013R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/libretv/android/utils/LocalProxyServer;", "", "okHttpClient", "Lokhttp3/OkHttpClient;", "adFilterInterceptor", "Lcom/libretv/android/utils/AdFilterInterceptor;", "<init>", "(Lokhttp3/OkHttpClient;Lcom/libretv/android/utils/AdFilterInterceptor;)V", "handleProxyRequest", "", "encodedUrl", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isM3U8Content", "", "url", "contentType", "getProxyUrl", "originalUrl", "start", "", "stop", "Companion", "app_debug"})
public final class LocalProxyServer {
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient okHttpClient = null;
    @org.jetbrains.annotations.NotNull()
    private final com.libretv.android.utils.AdFilterInterceptor adFilterInterceptor = null;
    private static final int PROXY_PORT = 8080;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PROXY_HOST = "localhost";
    @org.jetbrains.annotations.NotNull()
    public static final com.libretv.android.utils.LocalProxyServer.Companion Companion = null;
    
    public LocalProxyServer(@org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient okHttpClient, @org.jetbrains.annotations.NotNull()
    com.libretv.android.utils.AdFilterInterceptor adFilterInterceptor) {
        super();
    }
    
    /**
     * 处理代理请求
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object handleProxyRequest(@org.jetbrains.annotations.NotNull()
    java.lang.String encodedUrl, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * 判断是否为M3U8内容
     */
    private final boolean isM3U8Content(java.lang.String url, java.lang.String contentType) {
        return false;
    }
    
    /**
     * 获取代理URL
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getProxyUrl(@org.jetbrains.annotations.NotNull()
    java.lang.String originalUrl) {
        return null;
    }
    
    /**
     * 启动代理服务器（简化版本，实际使用OkHttp拦截器）
     */
    public final void start() {
    }
    
    /**
     * 停止代理服务器
     */
    public final void stop() {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/libretv/android/utils/LocalProxyServer$Companion;", "", "<init>", "()V", "PROXY_PORT", "", "PROXY_HOST", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}